@extends('layouts.shop')

@section('title', 'Checkout - PolyPay Store')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
            <div class="px-6 py-8">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Checkout</h1>

                @if(session('error'))
                    <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                        {{ session('error') }}
                    </div>
                @endif

                <form action="{{ route('checkout.store') }}" method="POST" class="space-y-8">
                    @csrf
                    <input type="hidden" name="product_id" value="{{ $product->id }}">
                    <input type="hidden" name="quantity" value="{{ $quantity }}">

                    <!-- Order Summary -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Order Summary</h2>
                        
                        <div class="flex items-center space-x-4 mb-4">
                            @if($product->primaryImage)
                                <img src="{{ asset('storage/' . $product->primaryImage->image_path) }}" alt="{{ $product->name }}" class="w-16 h-16 object-cover rounded">
                            @else
                                <div class="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center">
                                    <span class="text-2xl">📦</span>
                                </div>
                            @endif
                            <div class="flex-1">
                                <h3 class="font-semibold text-gray-900 dark:text-white">{{ $product->name }}</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    {{ ucfirst($product->type) }} Product
                                </p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    Quantity: {{ $quantity }}
                                </p>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-gray-900 dark:text-white">
                                    ${{ number_format($product->price, 2) }}
                                </p>
                            </div>
                        </div>

                        <div class="border-t border-gray-200 dark:border-gray-600 pt-4 space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Subtotal:</span>
                                <span class="text-gray-900 dark:text-white">${{ number_format($subtotal, 2) }}</span>
                            </div>
                            @if($shipping > 0)
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Shipping:</span>
                                <span class="text-gray-900 dark:text-white">${{ number_format($shipping, 2) }}</span>
                            </div>
                            @endif
                            <div class="flex justify-between text-lg font-semibold border-t border-gray-200 dark:border-gray-600 pt-2">
                                <span class="text-gray-900 dark:text-white">Total:</span>
                                <span class="text-gray-900 dark:text-white">${{ number_format($total, 2) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Information -->
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Customer Information</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="customer_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Full Name *
                                </label>
                                <input type="text" id="customer_name" name="customer_name" required
                                    value="{{ old('customer_name', auth()->user()->name ?? '') }}"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                @error('customer_name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="customer_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Email Address *
                                </label>
                                <input type="email" id="customer_email" name="customer_email" required
                                    value="{{ old('customer_email', auth()->user()->email ?? '') }}"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                @error('customer_email')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="md:col-span-2">
                                <label for="customer_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Phone Number
                                </label>
                                <input type="tel" id="customer_phone" name="customer_phone"
                                    value="{{ old('customer_phone', auth()->user()->phone ?? '') }}"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                @error('customer_phone')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Shipping Address (for physical products) -->
                    @if($product->type === 'physical')
                    <div id="shipping-section">
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Shipping Address</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="md:col-span-2">
                                <label for="address_line_1" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Street Address *
                                </label>
                                <input type="text" id="address_line_1" name="shipping_address[address_line_1]" required
                                    value="{{ old('shipping_address.address_line_1') }}"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                @error('shipping_address.address_line_1')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="md:col-span-2">
                                <label for="address_line_2" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Apartment, suite, etc. (optional)
                                </label>
                                <input type="text" id="address_line_2" name="shipping_address[address_line_2]"
                                    value="{{ old('shipping_address.address_line_2') }}"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                @error('shipping_address.address_line_2')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="city" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    City *
                                </label>
                                <input type="text" id="city" name="shipping_address[city]" required
                                    value="{{ old('shipping_address.city') }}"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                @error('shipping_address.city')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="state" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    State/Province *
                                </label>
                                <input type="text" id="state" name="shipping_address[state]" required
                                    value="{{ old('shipping_address.state') }}"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                @error('shipping_address.state')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="postal_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Postal Code *
                                </label>
                                <input type="text" id="postal_code" name="shipping_address[postal_code]" required
                                    value="{{ old('shipping_address.postal_code') }}"
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                @error('shipping_address.postal_code')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="country" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Country *
                                </label>
                                <select id="country" name="shipping_address[country]" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                    <option value="">Select Country</option>
                                    <option value="US" {{ old('shipping_address.country') === 'US' ? 'selected' : '' }}>United States</option>
                                    <option value="CA" {{ old('shipping_address.country') === 'CA' ? 'selected' : '' }}>Canada</option>
                                    <option value="GB" {{ old('shipping_address.country') === 'GB' ? 'selected' : '' }}>United Kingdom</option>
                                    <option value="AU" {{ old('shipping_address.country') === 'AU' ? 'selected' : '' }}>Australia</option>
                                    <option value="DE" {{ old('shipping_address.country') === 'DE' ? 'selected' : '' }}>Germany</option>
                                    <option value="FR" {{ old('shipping_address.country') === 'FR' ? 'selected' : '' }}>France</option>
                                    <option value="JP" {{ old('shipping_address.country') === 'JP' ? 'selected' : '' }}>Japan</option>
                                    <option value="other" {{ old('shipping_address.country') === 'other' ? 'selected' : '' }}>Other</option>
                                </select>
                                @error('shipping_address.country')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Payment Method -->
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Payment Method</h2>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <input id="usdt" name="crypto_currency" type="radio" value="USDT" 
                                    {{ old('crypto_currency', 'USDT') === 'USDT' ? 'checked' : '' }}
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <label for="usdt" class="ml-3 flex items-center">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">USDT (Tether)</span>
                                    <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                        Polygon Network
                                    </span>
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input id="pol" name="crypto_currency" type="radio" value="POL"
                                    {{ old('crypto_currency') === 'POL' ? 'checked' : '' }}
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <label for="pol" class="ml-3 flex items-center">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">POL (Polygon)</span>
                                    <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                        Polygon Network
                                    </span>
                                </label>
                            </div>
                        </div>
                        @error('crypto_currency')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-600">
                        <a href="{{ route('shop.product', $product->slug) }}" 
                            class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                            ← Back to Product
                        </a>
                        <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-lg transition-colors duration-300">
                            Proceed to Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
