<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Order;
use App\Services\DigitalDeliveryService;

class CustomerDashboardController extends Controller
{
    protected $digitalDeliveryService;

    public function __construct(DigitalDeliveryService $digitalDeliveryService)
    {
        $this->digitalDeliveryService = $digitalDeliveryService;
    }

    /**
     * Show customer dashboard
     */
    public function index()
    {
        $user = auth()->user();

        // Get user's orders
        $recentOrders = $user->orders()
            ->with(['payment', 'items.product'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get statistics
        $stats = [
            'total_orders' => $user->orders()->count(),
            'total_spent' => $user->orders()->whereHas('payment', function($query) {
                $query->where('status', 'confirmed');
            })->sum('total_amount'),
            'pending_orders' => $user->orders()->where('status', 'pending')->count(),
            'digital_products' => $user->orders()
                ->whereHas('items.product', function($query) {
                    $query->where('type', 'digital');
                })
                ->whereHas('payment', function($query) {
                    $query->where('status', 'confirmed');
                })
                ->count(),
        ];

        return view('customer.dashboard', compact('recentOrders', 'stats'));
    }

    /**
     * Show all orders
     */
    public function orders(Request $request)
    {
        $user = auth()->user();

        $query = $user->orders()->with(['payment', 'items.product']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $orders = $query->orderBy('created_at', 'desc')->paginate(10);

        return view('customer.orders', compact('orders'));
    }

    /**
     * Show order details
     */
    public function orderShow(Order $order)
    {
        // Ensure user can only view their own orders
        if ($order->user_id !== auth()->id()) {
            abort(403);
        }

        $order->load(['payment.wallet', 'items.product.digitalProduct']);

        // Get delivery status for digital products
        $deliveryStatus = $this->digitalDeliveryService->getDeliveryStatus($order);

        return view('customer.order-details', compact('order', 'deliveryStatus'));
    }

    /**
     * Show digital downloads
     */
    public function downloads()
    {
        $user = auth()->user();

        // Get all digital products from confirmed orders
        $digitalItems = collect();

        $orders = $user->orders()
            ->whereHas('payment', function($query) {
                $query->where('status', 'confirmed');
            })
            ->with(['items.product.digitalProduct'])
            ->get();

        foreach ($orders as $order) {
            foreach ($order->items as $item) {
                if ($item->product->isDigital() && $item->is_delivered) {
                    $digitalItems->push($item);
                }
            }
        }

        return view('customer.downloads', compact('digitalItems'));
    }
}
