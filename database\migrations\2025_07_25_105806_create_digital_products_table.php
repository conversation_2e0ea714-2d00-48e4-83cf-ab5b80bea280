<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('digital_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->enum('digital_type', ['file', 'text', 'image', 'audio', 'account_credentials', 'other']);
            $table->string('file_path')->nullable(); // for file-based digital products
            $table->text('content')->nullable(); // for text-based digital products
            $table->json('account_data')->nullable(); // for account credentials (encrypted)
            $table->integer('total_quantity')->default(1); // for bulk selling (e.g., 10 accounts)
            $table->integer('available_quantity')->default(1); // remaining quantity
            $table->boolean('is_unlimited')->default(false); // for unlimited digital products
            $table->integer('download_limit')->nullable(); // max downloads per purchase
            $table->integer('download_expiry_days')->nullable(); // days until download expires
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('digital_products');
    }
};
