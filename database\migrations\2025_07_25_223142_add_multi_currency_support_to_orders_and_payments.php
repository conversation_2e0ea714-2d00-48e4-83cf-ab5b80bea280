<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add multi-currency support to orders table
        Schema::table('orders', function (Blueprint $table) {
            $table->string('original_currency', 3)->default('USD')->after('currency');
            $table->decimal('original_amount', 10, 2)->nullable()->after('original_currency');
            $table->decimal('fiat_to_usd_rate', 10, 6)->default(1.0)->after('original_amount');
            $table->timestamp('exchange_rate_timestamp')->nullable()->after('fiat_to_usd_rate');
        });

        // Add multi-currency support to payments table
        Schema::table('payments', function (Blueprint $table) {
            $table->string('original_currency', 3)->default('USD')->after('amount_usd');
            $table->decimal('original_amount', 10, 2)->nullable()->after('original_currency');
            $table->decimal('crypto_price_usd', 15, 8)->nullable()->after('original_amount');
            $table->decimal('fiat_to_usd_rate', 10, 6)->default(1.0)->after('crypto_price_usd');
            $table->timestamp('conversion_timestamp')->nullable()->after('fiat_to_usd_rate');
            $table->json('conversion_data')->nullable()->after('conversion_timestamp');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn([
                'original_currency',
                'original_amount',
                'fiat_to_usd_rate',
                'exchange_rate_timestamp'
            ]);
        });

        Schema::table('payments', function (Blueprint $table) {
            $table->dropColumn([
                'original_currency',
                'original_amount',
                'crypto_price_usd',
                'fiat_to_usd_rate',
                'conversion_timestamp',
                'conversion_data'
            ]);
        });
    }
};
