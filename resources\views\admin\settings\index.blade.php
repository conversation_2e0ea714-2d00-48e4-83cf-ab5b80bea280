@extends('layouts.admin')

@section('title', 'Settings')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Settings</h1>
    </div>

    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {{ session('success') }}
        </div>
    @endif

    @if($errors->any())
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <ul>
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Checkout Authentication Settings -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                🔐 Checkout Authentication
            </h2>
            
            <form action="{{ route('admin.settings.update') }}" method="POST">
                @csrf
                
                <div class="space-y-4">
                    <!-- Checkout Requires Login -->
                    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div>
                            <label class="text-sm font-medium text-gray-900 dark:text-white">
                                Require Login for Checkout
                            </label>
                            <p class="text-xs text-gray-600 dark:text-gray-400">
                                Force customers to log in before they can checkout
                            </p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="checkout_requires_login" value="1" 
                                   {{ $settings['checkout_requires_login'] ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <!-- Guest Checkout Enabled -->
                    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div>
                            <label class="text-sm font-medium text-gray-900 dark:text-white">
                                Allow Guest Checkout
                            </label>
                            <p class="text-xs text-gray-600 dark:text-gray-400">
                                Allow customers to checkout without creating an account
                            </p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="guest_checkout_enabled" value="1" 
                                   {{ $settings['guest_checkout_enabled'] ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                    </div>

                    <!-- Current Status Display -->
                    <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <h3 class="text-sm font-medium text-blue-900 dark:text-blue-200 mb-2">Current Status:</h3>
                        <div class="space-y-1 text-sm">
                            @if($settings['checkout_requires_login'])
                                <div class="text-orange-600 dark:text-orange-400">
                                    ⚠️ Customers must log in to checkout
                                </div>
                            @elseif($settings['guest_checkout_enabled'])
                                <div class="text-green-600 dark:text-green-400">
                                    ✅ Guest checkout is allowed
                                </div>
                            @else
                                <div class="text-red-600 dark:text-red-400">
                                    ❌ Both login required and guest checkout disabled
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
        </div>

        <!-- Currency Settings -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                💰 Currency Settings
            </h2>
            
            <div class="space-y-4">
                <!-- Default Product Currency -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Default Currency for New Products
                    </label>
                    <select name="default_product_currency" 
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        @foreach($supportedCurrencies as $code => $name)
                            <option value="{{ $code }}" {{ $settings['default_product_currency'] === $code ? 'selected' : '' }}>
                                {{ $name }}
                            </option>
                        @endforeach
                    </select>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        This currency will be pre-selected when creating new products
                    </p>
                </div>

                <!-- Currency Info -->
                <div class="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                    <h3 class="text-sm font-medium text-yellow-900 dark:text-yellow-200 mb-2">💡 How Currency Control Works:</h3>
                    <ul class="space-y-1 text-sm text-yellow-800 dark:text-yellow-300">
                        <li>• Each product has its own base currency set by admin</li>
                        <li>• Product prices are always shown in their admin-defined currency</li>
                        <li>• Customer currency selector only affects display format</li>
                        <li>• Payments are processed using the product's base currency</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Save Button -->
    <div class="mt-6 flex justify-end">
        <button type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition duration-200">
            💾 Save Settings
        </button>
    </div>
    </form>
</div>
@endsection
