@extends('layouts.admin')

@section('title', $product->name . ' - Product Details')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Product Details</h1>
        <div class="flex space-x-3">
            <a href="{{ route('admin.products.edit', $product) }}" 
               class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                ✏️ Edit Product
            </a>
            <a href="{{ route('admin.products.index') }}" 
               class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                ← Back to Products
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Product Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">📦 Basic Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Product Name</label>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $product->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">SKU</label>
                        <p class="text-gray-900 dark:text-white font-mono">{{ $product->sku ?? 'Not set' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category</label>
                        <p class="text-gray-900 dark:text-white">
                            @if($product->category)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    {{ $product->category->name }}
                                </span>
                            @else
                                <span class="text-gray-500">No category</span>
                            @endif
                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Product Type</label>
                        <p class="text-gray-900 dark:text-white">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                {{ $product->type === 'digital' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' }}">
                                {{ ucfirst($product->type) }}
                            </span>
                        </p>
                    </div>
                </div>

                @if($product->short_description)
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Short Description</label>
                    <p class="text-gray-900 dark:text-white">{{ $product->short_description }}</p>
                </div>
                @endif

                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
                    <div class="prose dark:prose-invert max-w-none">
                        <p class="text-gray-900 dark:text-white">{{ $product->description }}</p>
                    </div>
                </div>
            </div>

            <!-- Digital Product Information -->
            @if($product->type === 'digital' && $product->digitalProduct)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">💾 Digital Product Details</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Download Limit</label>
                        <p class="text-gray-900 dark:text-white">
                            {{ $product->digitalProduct->download_limit ?? 'Unlimited' }}
                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Access Duration</label>
                        <p class="text-gray-900 dark:text-white">
                            {{ $product->digitalProduct->access_duration_days ? $product->digitalProduct->access_duration_days . ' days' : 'Lifetime' }}
                        </p>
                    </div>
                </div>

                @if($product->digitalProduct->file_path)
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">File</label>
                    <p class="text-gray-900 dark:text-white font-mono text-sm bg-gray-100 dark:bg-gray-700 p-2 rounded">
                        {{ basename($product->digitalProduct->file_path) }}
                    </p>
                </div>
                @endif

                @if($product->digitalProduct->download_instructions)
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Download Instructions</label>
                    <p class="text-gray-900 dark:text-white">{{ $product->digitalProduct->download_instructions }}</p>
                </div>
                @endif
            </div>
            @endif
        </div>

        <!-- Sidebar Information -->
        <div class="space-y-6">
            <!-- Pricing & Currency -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">💰 Pricing & Currency</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Base Currency</label>
                        <div class="flex items-center space-x-2">
                            <span class="text-2xl">
                                @switch($product->base_currency)
                                    @case('USD') $ @break
                                    @case('EUR') € @break
                                    @case('GBP') £ @break
                                    @case('CAD') C$ @break
                                    @case('AUD') A$ @break
                                    @case('JPY') ¥ @break
                                    @default {{ $product->base_currency }}
                                @endswitch
                            </span>
                            <span class="text-lg font-semibold text-gray-900 dark:text-white">{{ $product->base_currency }}</span>
                        </div>
                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                            Admin-controlled currency for this product
                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Price</label>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white">
                            {{ $product->getFormattedPrice() }}
                        </p>
                    </div>
                    
                    @if($product->compare_price)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Compare Price</label>
                        <p class="text-xl text-gray-500 dark:text-gray-400 line-through">
                            {{ $product->getFormattedComparePrice() }}
                        </p>
                        <p class="text-sm text-green-600 dark:text-green-400 font-medium">
                            Save {{ $product->getFormattedSavings() }}
                        </p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Stock & Status -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">📊 Stock & Status</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            {{ $product->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                            {{ $product->is_active ? '✅ Active' : '❌ Inactive' }}
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Featured</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            {{ $product->is_featured ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200' }}">
                            {{ $product->is_featured ? '⭐ Featured' : 'Not Featured' }}
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Stock Tracking</label>
                        <p class="text-gray-900 dark:text-white">
                            {{ $product->track_stock ? 'Enabled' : 'Disabled' }}
                        </p>
                    </div>
                    
                    @if($product->track_stock)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Stock Quantity</label>
                        <p class="text-2xl font-bold {{ $product->stock_quantity <= 5 ? 'text-red-600' : 'text-green-600' }}">
                            {{ $product->stock_quantity }}
                        </p>
                        @if($product->stock_quantity <= 5)
                        <p class="text-xs text-red-600 dark:text-red-400">⚠️ Low stock alert</p>
                        @endif
                    </div>
                    @endif
                </div>
            </div>

            <!-- Metadata -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">📅 Metadata</h2>
                
                <div class="space-y-3 text-sm">
                    <div>
                        <label class="block text-gray-700 dark:text-gray-300 font-medium">Created</label>
                        <p class="text-gray-900 dark:text-white">{{ $product->created_at->format('M j, Y \a\t g:i A') }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 dark:text-gray-300 font-medium">Last Updated</label>
                        <p class="text-gray-900 dark:text-white">{{ $product->updated_at->format('M j, Y \a\t g:i A') }}</p>
                    </div>
                    
                    @if($product->weight)
                    <div>
                        <label class="block text-gray-700 dark:text-gray-300 font-medium">Weight</label>
                        <p class="text-gray-900 dark:text-white">{{ $product->weight }} kg</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">⚡ Quick Actions</h2>
                
                <div class="space-y-3">
                    <a href="{{ route('admin.products.edit', $product) }}" 
                       class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                        ✏️ Edit Product
                    </a>
                    
                    <a href="{{ url('/product/' . $product->slug) }}" target="_blank"
                       class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                        👁️ View in Shop
                    </a>
                    
                    <form action="{{ route('admin.products.toggle-status', $product) }}" method="POST" class="w-full">
                        @csrf
                        @method('PATCH')
                        <button type="submit" 
                                class="w-full {{ $product->is_active ? 'bg-orange-600 hover:bg-orange-700' : 'bg-green-600 hover:bg-green-700' }} text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                            {{ $product->is_active ? '⏸️ Deactivate' : '▶️ Activate' }}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
