@extends('layouts.admin')

@section('title', 'Edit Product - ' . $product->name)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Edit Product</h1>
        <div class="flex space-x-3">
            <a href="{{ route('admin.products.show', $product) }}" 
               class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                👁️ View Product
            </a>
            <a href="{{ route('admin.products.index') }}" 
               class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                ← Back to Products
            </a>
        </div>
    </div>

    @if($errors->any())
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <ul>
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {{ session('success') }}
        </div>
    @endif

    <form action="{{ route('admin.products.update', $product) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
        @csrf
        @method('PUT')
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Basic Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">📦 Basic Information</h2>
                
                <div class="space-y-4">
                    <!-- Product Name -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Product Name *
                        </label>
                        <input type="text" name="name" value="{{ old('name', $product->name) }}" required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>

                    <!-- Short Description -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Short Description
                        </label>
                        <textarea name="short_description" rows="2"
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">{{ old('short_description', $product->short_description) }}</textarea>
                    </div>

                    <!-- Description -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Description *
                        </label>
                        <textarea name="description" rows="4" required
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">{{ old('description', $product->description) }}</textarea>
                    </div>

                    <!-- Category -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Category
                        </label>
                        <select name="category_id"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                            <option value="">Select Category</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ old('category_id', $product->category_id) == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Product Type -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Product Type *
                        </label>
                        <select name="type" required
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                            <option value="physical" {{ old('type', $product->type) === 'physical' ? 'selected' : '' }}>Physical Product</option>
                            <option value="digital" {{ old('type', $product->type) === 'digital' ? 'selected' : '' }}>Digital Product</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Pricing & Currency -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">💰 Pricing & Currency</h2>
                
                <div class="space-y-4">
                    <!-- Current Currency Display -->
                    <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <h3 class="text-sm font-medium text-blue-900 dark:text-blue-200 mb-2">Current Currency:</h3>
                        <div class="flex items-center space-x-2">
                            <span class="text-2xl">
                                @switch($product->base_currency)
                                    @case('USD') $ @break
                                    @case('EUR') € @break
                                    @case('GBP') £ @break
                                    @case('CAD') C$ @break
                                    @case('AUD') A$ @break
                                    @case('JPY') ¥ @break
                                    @default {{ $product->base_currency }}
                                @endswitch
                            </span>
                            <span class="text-lg font-semibold text-blue-900 dark:text-blue-200">{{ $product->base_currency }}</span>
                        </div>
                    </div>

                    <!-- Base Currency -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Base Currency *
                        </label>
                        <select name="base_currency" required
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                            @foreach($supportedCurrencies as $code => $name)
                                <option value="{{ $code }}" {{ old('base_currency', $product->base_currency) === $code ? 'selected' : '' }}>
                                    {{ $name }}
                                </option>
                            @endforeach
                        </select>
                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                            ⚠️ Changing currency will affect all pricing and payments for this product
                        </p>
                    </div>

                    <!-- Price -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Price *
                        </label>
                        <input type="number" name="price" value="{{ old('price', $product->price) }}" step="0.01" min="0" required
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>

                    <!-- Compare Price -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Compare Price (Optional)
                        </label>
                        <input type="number" name="compare_price" value="{{ old('compare_price', $product->compare_price) }}" step="0.01" min="0"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                            Show a crossed-out higher price to indicate savings
                        </p>
                    </div>

                    <!-- Currency Control Info -->
                    <div class="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                        <h3 class="text-sm font-medium text-yellow-900 dark:text-yellow-200 mb-2">💡 Currency Control:</h3>
                        <ul class="space-y-1 text-sm text-yellow-800 dark:text-yellow-300">
                            <li>• Product prices are always shown in the selected base currency</li>
                            <li>• Customer currency selector won't override this setting</li>
                            <li>• Payments will be processed in this currency</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory & Settings -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">📊 Inventory & Settings</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Track Stock -->
                <div class="flex items-center">
                    <input type="checkbox" name="track_stock" value="1" {{ old('track_stock', $product->track_stock) ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Track Stock Quantity
                    </label>
                </div>

                <!-- Stock Quantity -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Stock Quantity
                    </label>
                    <input type="number" name="stock_quantity" value="{{ old('stock_quantity', $product->stock_quantity) }}" min="0"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                </div>

                <!-- Is Active -->
                <div class="flex items-center">
                    <input type="checkbox" name="is_active" value="1" {{ old('is_active', $product->is_active) ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Product is Active
                    </label>
                </div>

                <!-- Is Featured -->
                <div class="flex items-center">
                    <input type="checkbox" name="is_featured" value="1" {{ old('is_featured', $product->is_featured) ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Featured Product
                    </label>
                </div>
            </div>

            <!-- Weight (for physical products) -->
            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Weight (kg) - Optional
                </label>
                <input type="number" name="weight" value="{{ old('weight', $product->weight) }}" step="0.01" min="0"
                       class="w-full md:w-1/3 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
            </div>
        </div>

        <!-- Digital Product Settings -->
        @if($product->type === 'digital')
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">💾 Digital Product Settings</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Download Limit -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Download Limit
                    </label>
                    <input type="number" name="download_limit" 
                           value="{{ old('download_limit', $product->digitalProduct->download_limit ?? '') }}" 
                           min="1" placeholder="Leave empty for unlimited"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                </div>

                <!-- Access Duration -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Access Duration (days)
                    </label>
                    <input type="number" name="access_duration_days" 
                           value="{{ old('access_duration_days', $product->digitalProduct->access_duration_days ?? '') }}" 
                           min="1" placeholder="Leave empty for lifetime access"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                </div>
            </div>

            <!-- Download Instructions -->
            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Download Instructions
                </label>
                <textarea name="download_instructions" rows="3" placeholder="Instructions for customers on how to download/access the product"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">{{ old('download_instructions', $product->digitalProduct->download_instructions ?? '') }}</textarea>
            </div>

            <!-- File Upload -->
            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Digital File
                </label>
                @if($product->digitalProduct && $product->digitalProduct->file_path)
                    <div class="mb-2 p-3 bg-gray-100 dark:bg-gray-700 rounded-md">
                        <p class="text-sm text-gray-700 dark:text-gray-300">
                            Current file: <span class="font-mono">{{ basename($product->digitalProduct->file_path) }}</span>
                        </p>
                    </div>
                @endif
                <input type="file" name="digital_file" accept=".pdf,.zip,.rar,.exe,.dmg,.pkg,.deb,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    Upload a new file to replace the current one. Supported formats: PDF, ZIP, RAR, EXE, DMG, PKG, DEB, TXT, DOC, XLS, PPT
                </p>
            </div>
        </div>
        @endif

        <!-- Submit Buttons -->
        <div class="flex justify-end space-x-4">
            <a href="{{ route('admin.products.show', $product) }}" 
               class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-6 rounded-lg transition duration-200">
                Cancel
            </a>
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition duration-200">
                💾 Update Product
            </button>
        </div>
    </form>
</div>
@endsection
