<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Polygon Network Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Polygon network connections and smart contracts
    |
    */

    'polygon_rpc_url' => env('POLYGON_RPC_URL', 'https://polygon-rpc.com'),
    'polygon_chain_id' => env('POLYGON_CHAIN_ID', 137),
    
    'polygon_testnet_rpc_url' => env('POLYGON_TESTNET_RPC_URL', 'https://rpc-mumbai.maticvigil.com'),
    'polygon_testnet_chain_id' => env('POLYGON_TESTNET_CHAIN_ID', 80001),
    
    'use_testnet' => env('USE_TESTNET', true),

    /*
    |--------------------------------------------------------------------------
    | Admin Wallet Configuration
    |--------------------------------------------------------------------------
    |
    | The admin wallet that will receive all payments
    |
    */

    'admin_wallet_address' => env('ADMIN_WALLET_ADDRESS'),
    'admin_wallet_private_key' => env('ADMIN_WALLET_PRIVATE_KEY'),

    /*
    |--------------------------------------------------------------------------
    | Token Contracts
    |--------------------------------------------------------------------------
    |
    | Smart contract addresses for supported tokens
    |
    */

    'contracts' => [
        'mainnet' => [
            'usdt' => env('USDT_CONTRACT_ADDRESS', '******************************************'),
            'pol' => env('POL_CONTRACT_ADDRESS', '******************************************'),
        ],
        'testnet' => [
            'usdt' => '******************************************', // Mumbai testnet USDT
            'pol' => '******************************************', // Mumbai testnet POL
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for payment processing
    |
    */

    'payment_timeout_minutes' => env('PAYMENT_TIMEOUT_MINUTES', 30),
    'confirmation_blocks' => 12, // Number of blocks to wait for confirmation
    'gas_limit' => 100000,
    'gas_price' => 20000000000, // 20 Gwei

    /*
    |--------------------------------------------------------------------------
    | Supported Currencies
    |--------------------------------------------------------------------------
    |
    | List of supported cryptocurrency currencies
    |
    */

    'supported_currencies' => ['USDT', 'POL'],

    /*
    |--------------------------------------------------------------------------
    | Token Decimals
    |--------------------------------------------------------------------------
    |
    | Decimal places for each token
    |
    */

    'token_decimals' => [
        'USDT' => 6,
        'POL' => 18,
        'MATIC' => 18,
    ],
];
