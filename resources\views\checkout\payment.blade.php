@extends('layouts.shop')

@section('title', 'Payment - PolyPay Store')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
            <div class="px-6 py-8">
                <div class="text-center mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">Complete Your Payment</h1>
                    <p class="text-gray-600 dark:text-gray-400">
                        Send {{ $payment->formatted_amount }} to the address below
                    </p>
                </div>

                <!-- Payment Status -->
                <div class="mb-8 p-4 rounded-lg {{ $payment->status === 'confirmed' ? 'bg-green-100 border border-green-400' : 'bg-yellow-100 border border-yellow-400' }}">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            @if($payment->status === 'confirmed')
                                <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                            @else
                                <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                </svg>
                            @endif
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium {{ $payment->status === 'confirmed' ? 'text-green-800' : 'text-yellow-800' }}">
                                @if($payment->status === 'confirmed')
                                    Payment Confirmed!
                                @else
                                    Waiting for Payment...
                                @endif
                            </p>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Payment Details -->
                    <div class="space-y-6">
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Payment Details</h2>
                            
                            <div class="space-y-4">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Amount:</span>
                                    <span class="font-semibold text-gray-900 dark:text-white">
                                        {{ number_format($payment->amount, 8) }} {{ $payment->crypto_currency }}
                                    </span>
                                </div>
                                
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">USD Value:</span>
                                    <span class="font-semibold text-gray-900 dark:text-white">
                                        ${{ number_format($payment->amount_usd, 2) }}
                                    </span>
                                </div>
                                
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Network:</span>
                                    <span class="font-semibold text-gray-900 dark:text-white">Polygon</span>
                                </div>
                                
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Payment ID:</span>
                                    <span class="font-mono text-sm text-gray-900 dark:text-white">{{ $payment->payment_id }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Wallet Address -->
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Send Payment To:</h3>
                            
                            <div class="bg-white dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
                                <div class="text-center">
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Wallet Address:</p>
                                    <p class="font-mono text-sm break-all text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-900 p-3 rounded">
                                        {{ $payment->wallet_address }}
                                    </p>
                                    <button onclick="copyToClipboard('{{ $payment->wallet_address }}')" 
                                        class="mt-3 inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                        </svg>
                                        Copy Address
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Timer -->
                        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                            <div class="flex items-center">
                                <svg class="h-5 w-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-red-800 dark:text-red-200">Payment expires in:</p>
                                    <p id="countdown" class="text-lg font-bold text-red-900 dark:text-red-100"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="space-y-6">
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Order Summary</h2>
                            
                            @foreach($payment->order->items as $item)
                            <div class="flex items-center space-x-4 mb-4">
                                @if($item->product->primaryImage)
                                    <img src="{{ asset('storage/' . $item->product->primaryImage->image_path) }}" alt="{{ $item->product_name }}" class="w-12 h-12 object-cover rounded">
                                @else
                                    <div class="w-12 h-12 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center">
                                        <span class="text-lg">📦</span>
                                    </div>
                                @endif
                                <div class="flex-1">
                                    <h3 class="font-medium text-gray-900 dark:text-white">{{ $item->product_name }}</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Qty: {{ $item->quantity }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium text-gray-900 dark:text-white">${{ number_format($item->total, 2) }}</p>
                                </div>
                            </div>
                            @endforeach

                            <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                                <div class="flex justify-between text-lg font-semibold">
                                    <span class="text-gray-900 dark:text-white">Total:</span>
                                    <span class="text-gray-900 dark:text-white">${{ number_format($payment->order->total_amount, 2) }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Instructions -->
                        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                            <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">Payment Instructions</h3>
                            <ol class="list-decimal list-inside space-y-2 text-sm text-blue-800 dark:text-blue-200">
                                <li>Copy the wallet address above</li>
                                <li>Open your crypto wallet (MetaMask, Trust Wallet, etc.)</li>
                                <li>Send exactly {{ number_format($payment->amount, 8) }} {{ $payment->crypto_currency }} to the address</li>
                                <li>Wait for transaction confirmation</li>
                                <li>You'll be automatically redirected when payment is confirmed</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Countdown timer
const expiresAt = new Date('{{ $payment->expires_at->toISOString() }}');

function updateCountdown() {
    const now = new Date();
    const timeLeft = expiresAt - now;
    
    if (timeLeft <= 0) {
        document.getElementById('countdown').textContent = 'Expired';
        return;
    }
    
    const minutes = Math.floor(timeLeft / 60000);
    const seconds = Math.floor((timeLeft % 60000) / 1000);
    
    document.getElementById('countdown').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

// Update countdown every second
setInterval(updateCountdown, 1000);
updateCountdown();

// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        alert('Address copied to clipboard!');
    });
}

// Check payment status every 10 seconds
setInterval(function() {
    fetch(window.location.href, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'confirmed') {
            window.location.href = '{{ route("checkout.success", $payment->order) }}';
        }
    })
    .catch(error => console.log('Status check failed:', error));
}, 10000);
</script>
@endsection
