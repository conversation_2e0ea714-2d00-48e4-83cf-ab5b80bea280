@props(['amount', 'currency' => 'USD', 'cryptoCurrency' => 'USDT'])

@php
    use App\Helpers\CurrencyHelper;
    
    $selectedCurrency = session('selected_currency', 'USD');
    $displayAmount = $amount;
    
    // Convert amount to selected currency if different from USD
    if ($selectedCurrency !== 'USD' && $currency === 'USD') {
        $displayAmount = CurrencyHelper::convert($amount, 'USD', $selectedCurrency);
        $currency = $selectedCurrency;
    }
    
    // Get crypto conversion preview
    $cryptoPreview = CurrencyHelper::getCryptoPreview($displayAmount, $currency, $cryptoCurrency);
@endphp

<div class="crypto-price-display bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
    <div class="flex items-center justify-between mb-3">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Payment Amount</h3>
        <button onclick="refreshPrices()" 
                class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
                id="refresh-btn">
            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Refresh
        </button>
    </div>
    
    <div class="space-y-3">
        <!-- Fiat Amount -->
        <div class="flex justify-between items-center">
            <span class="text-gray-600 dark:text-gray-400">Amount in {{ $currency }}:</span>
            <span class="text-xl font-bold text-gray-900 dark:text-white" id="fiat-amount">
                {{ CurrencyHelper::format($displayAmount, $currency) }}
            </span>
        </div>
        
        <!-- Crypto Amount -->
        <div class="flex justify-between items-center">
            <span class="text-gray-600 dark:text-gray-400">Amount in {{ $cryptoCurrency }}:</span>
            <span class="text-xl font-bold text-blue-600 dark:text-blue-400" id="crypto-amount">
                @if(isset($cryptoPreview['error']))
                    <span class="text-red-500 text-sm">{{ $cryptoPreview['error'] }}</span>
                @else
                    {{ $cryptoPreview['crypto_amount_formatted'] }} {{ $cryptoCurrency }}
                @endif
            </span>
        </div>
        
        @if(!isset($cryptoPreview['error']))
            <!-- Exchange Rate Info -->
            <div class="text-sm text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-600 pt-3">
                <div class="flex justify-between">
                    <span>{{ $cryptoCurrency }} Price:</span>
                    <span id="crypto-price">${{ number_format($cryptoPreview['crypto_price_usd'], 4) }}</span>
                </div>
                @if($currency !== 'USD')
                    <div class="flex justify-between">
                        <span>{{ $currency }}/USD Rate:</span>
                        <span id="fiat-rate">{{ number_format($cryptoPreview['fiat_to_usd_rate'], 4) }}</span>
                    </div>
                @endif
                <div class="flex justify-between text-xs mt-1">
                    <span>Last updated:</span>
                    <span id="last-updated">{{ now()->format('H:i:s') }}</span>
                </div>
            </div>
        @endif
    </div>
    
    <!-- Price Change Indicator -->
    @php
        $cryptoPrices = CurrencyHelper::getCryptoPrices();
        $priceChange = $cryptoPrices[$cryptoCurrency]['change_24h'] ?? 0;
    @endphp
    
    @if($priceChange != 0)
        <div class="mt-3 text-sm">
            <span class="text-gray-600 dark:text-gray-400">24h change: </span>
            <span class="{{ $priceChange >= 0 ? 'text-green-600' : 'text-red-600' }}">
                {{ $priceChange >= 0 ? '+' : '' }}{{ number_format($priceChange, 2) }}%
            </span>
        </div>
    @endif
</div>

<script>
let refreshTimeout;

async function refreshPrices() {
    const refreshBtn = document.getElementById('refresh-btn');
    const originalContent = refreshBtn.innerHTML;
    
    // Show loading state
    refreshBtn.innerHTML = '<svg class="w-4 h-4 inline mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Updating...';
    refreshBtn.disabled = true;
    
    try {
        const response = await fetch('/api/crypto-prices', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                amount: {{ $displayAmount }},
                currency: '{{ $currency }}',
                crypto_currency: '{{ $cryptoCurrency }}'
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            
            // Update displayed values
            document.getElementById('crypto-amount').textContent = data.crypto_amount_formatted + ' {{ $cryptoCurrency }}';
            document.getElementById('crypto-price').textContent = '$' + parseFloat(data.crypto_price_usd).toFixed(4);
            
            if (document.getElementById('fiat-rate') && data.fiat_to_usd_rate) {
                document.getElementById('fiat-rate').textContent = parseFloat(data.fiat_to_usd_rate).toFixed(4);
            }
            
            document.getElementById('last-updated').textContent = new Date().toLocaleTimeString();
            
            // Show success feedback
            refreshBtn.innerHTML = '<svg class="w-4 h-4 inline mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Updated';
            
            setTimeout(() => {
                refreshBtn.innerHTML = originalContent;
                refreshBtn.disabled = false;
            }, 2000);
        } else {
            throw new Error('Failed to fetch prices');
        }
    } catch (error) {
        console.error('Error refreshing prices:', error);
        refreshBtn.innerHTML = '<svg class="w-4 h-4 inline mr-1 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>Error';
        
        setTimeout(() => {
            refreshBtn.innerHTML = originalContent;
            refreshBtn.disabled = false;
        }, 2000);
    }
}

// Auto-refresh every 30 seconds
function startAutoRefresh() {
    refreshTimeout = setInterval(refreshPrices, 30000);
}

// Stop auto-refresh when page is hidden
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        clearInterval(refreshTimeout);
    } else {
        startAutoRefresh();
    }
});

// Start auto-refresh when component loads
document.addEventListener('DOMContentLoaded', startAutoRefresh);
</script>
