<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Wallet;
use App\Services\WalletService;
use App\Services\Web3Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Crypt;

class WalletController extends Controller
{
    protected $walletService;
    protected $web3Service;

    public function __construct(WalletService $walletService, Web3Service $web3Service)
    {
        $this->walletService = $walletService;
        $this->web3Service = $web3Service;
    }

    /**
     * Display admin wallet management dashboard
     */
    public function index()
    {
        // Get current admin wallet configuration
        $adminWalletAddress = config('web3.admin_wallet_address');
        $hasAdminWallet = !empty($adminWalletAddress);

        // Get wallet statistics
        $stats = [
            'total_wallets' => Wallet::count(),
            'active_wallets' => Wallet::where('status', 'active')->count(),
            'used_wallets' => Wallet::where('status', 'used')->count(),
            'expired_wallets' => Wallet::where('status', 'expired')->count(),
        ];

        // Get recent wallets
        $recentWallets = Wallet::with('payment.order')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get supported currencies
        $supportedCurrencies = config('web3.supported_currencies', ['USDT', 'POL']);

        return view('admin.wallet.index', compact(
            'adminWalletAddress',
            'hasAdminWallet',
            'stats',
            'recentWallets',
            'supportedCurrencies'
        ));
    }

    /**
     * Show form to set/update admin wallet
     */
    public function create()
    {
        $currentAddress = config('web3.admin_wallet_address');
        $supportedCurrencies = config('web3.supported_currencies', ['USDT', 'POL']);

        return view('admin.wallet.create', compact('currentAddress', 'supportedCurrencies'));
    }

    /**
     * Generate a new admin wallet
     */
    public function generate(Request $request)
    {
        try {
            // Generate new wallet
            $walletData = $this->walletService->generateWallet();

            return response()->json([
                'success' => true,
                'wallet' => $walletData,
                'message' => 'New wallet generated successfully!'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to generate admin wallet', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate wallet: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store/update admin wallet configuration
     */
    public function store(Request $request)
    {
        $request->validate([
            'wallet_address' => 'required|string|regex:/^0x[a-fA-F0-9]{40}$/',
            'private_key' => 'required|string|min:64|max:66',
        ], [
            'wallet_address.regex' => 'Please enter a valid Ethereum wallet address (0x...)',
            'private_key.min' => 'Private key must be at least 64 characters long',
            'private_key.max' => 'Private key must not exceed 66 characters',
        ]);

        try {
            // Validate the private key format
            $privateKey = $request->private_key;
            if (str_starts_with($privateKey, '0x')) {
                $privateKey = substr($privateKey, 2);
            }

            if (!ctype_xdigit($privateKey) || strlen($privateKey) !== 64) {
                return back()->withErrors(['private_key' => 'Invalid private key format']);
            }

            // Update environment file (in production, you'd want to use a more secure method)
            $this->updateEnvFile([
                'ADMIN_WALLET_ADDRESS' => $request->wallet_address,
                'ADMIN_WALLET_PRIVATE_KEY' => $privateKey,
            ]);

            // Clear config cache
            \Artisan::call('config:clear');

            Log::info('Admin wallet configuration updated', [
                'address' => $request->wallet_address,
                'updated_by' => auth()->id()
            ]);

            return redirect()->route('admin.wallet.index')
                ->with('success', 'Admin wallet configuration updated successfully!');

        } catch (\Exception $e) {
            Log::error('Failed to update admin wallet configuration', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return back()->withInput()
                ->with('error', 'Failed to update wallet configuration: ' . $e->getMessage());
        }
    }

    /**
     * Check wallet balance
     */
    public function checkBalance(Request $request)
    {
        $request->validate([
            'address' => 'required|string|regex:/^0x[a-fA-F0-9]{40}$/',
            'currency' => 'required|string|in:' . implode(',', config('web3.supported_currencies', ['USDT', 'POL']))
        ]);

        try {
            // Create a temporary wallet object for balance checking
            $tempWallet = new Wallet([
                'address' => $request->address,
                'currency' => $request->currency
            ]);

            $balance = $this->walletService->checkWalletBalance($tempWallet);

            return response()->json([
                'success' => true,
                'balance' => $balance,
                'currency' => $request->currency,
                'formatted_balance' => $balance . ' ' . $request->currency
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to check wallet balance', [
                'address' => $request->address,
                'currency' => $request->currency,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to check balance: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show wallet details
     */
    public function show(Wallet $wallet)
    {
        $wallet->load('payment.order');

        // Check current balance
        $balance = '0';
        try {
            $balance = $this->walletService->checkWalletBalance($wallet);
        } catch (\Exception $e) {
            Log::warning('Could not check wallet balance', [
                'wallet_id' => $wallet->id,
                'error' => $e->getMessage()
            ]);
        }

        return view('admin.wallet.show', compact('wallet', 'balance'));
    }

    /**
     * Clean up expired wallets
     */
    public function cleanup()
    {
        try {
            $expiredCount = $this->walletService->markExpiredWallets();
            $cleanedCount = $this->walletService->cleanupOldWallets(30);

            return back()->with('success', 
                "Wallet cleanup completed. Marked {$expiredCount} wallets as expired and cleaned up {$cleanedCount} old wallets."
            );

        } catch (\Exception $e) {
            Log::error('Wallet cleanup failed', ['error' => $e->getMessage()]);
            return back()->with('error', 'Wallet cleanup failed: ' . $e->getMessage());
        }
    }

    /**
     * Update environment file
     */
    private function updateEnvFile(array $data)
    {
        $envFile = base_path('.env');
        $envContent = file_get_contents($envFile);

        foreach ($data as $key => $value) {
            $pattern = "/^{$key}=.*/m";
            $replacement = "{$key}={$value}";

            if (preg_match($pattern, $envContent)) {
                $envContent = preg_replace($pattern, $replacement, $envContent);
            } else {
                $envContent .= "\n{$replacement}";
            }
        }

        file_put_contents($envFile, $envContent);
    }
}
