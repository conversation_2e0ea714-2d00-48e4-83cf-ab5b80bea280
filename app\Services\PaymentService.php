<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Payment;
use App\Models\Wallet;
use App\Services\DigitalDeliveryService;
use Illuminate\Support\Facades\Log;
use Exception;

class PaymentService
{
    protected $walletService;
    protected $digitalDeliveryService;
    protected $currencyService;

    public function __construct(
        WalletService $walletService,
        DigitalDeliveryService $digitalDeliveryService,
        CurrencyService $currencyService
    ) {
        $this->walletService = $walletService;
        $this->digitalDeliveryService = $digitalDeliveryService;
        $this->currencyService = $currencyService;
    }

    /**
     * Create a payment for an order
     */
    public function createPayment(Order $order, string $currency, float $amount, string $fiatCurrency = 'USD'): Payment
    {
        try {
            // Get real-time conversion data
            $conversionData = $this->currencyService->convertFiatToCrypto($amount, $fiatCurrency, $currency);

            // Generate wallet address first
            $walletData = $this->walletService->generateWallet();

            // Create payment record with wallet address and conversion data
            $payment = Payment::create([
                'order_id' => $order->id,
                'payment_method' => 'crypto',
                'crypto_currency' => $currency,
                'amount' => $conversionData['crypto_amount'],
                'amount_usd' => $conversionData['usd_amount'],
                'original_currency' => $fiatCurrency,
                'original_amount' => $amount,
                'crypto_price_usd' => $conversionData['crypto_price_usd'],
                'fiat_to_usd_rate' => $conversionData['fiat_to_usd_rate'],
                'conversion_timestamp' => now(),
                'conversion_data' => $conversionData,
                'wallet_address' => $walletData['address'],
                'status' => 'pending',
                'expires_at' => now()->addMinutes((int) config('web3.payment_timeout_minutes', 30)),
            ]);

            // Create wallet record for this payment
            $wallet = $this->walletService->createWalletRecord($payment, $walletData, $currency);

            Log::info('Created payment for order', [
                'order_id' => $order->id,
                'payment_id' => $payment->payment_id,
                'crypto_currency' => $currency,
                'crypto_amount' => $conversionData['crypto_amount'],
                'original_currency' => $fiatCurrency,
                'original_amount' => $amount,
                'usd_amount' => $conversionData['usd_amount'],
                'crypto_price_usd' => $conversionData['crypto_price_usd'],
                'wallet_address' => $wallet->address
            ]);

            return $payment;

        } catch (Exception $e) {
            Log::error('Failed to create payment', [
                'order_id' => $order->id,
                'crypto_currency' => $currency,
                'original_currency' => $fiatCurrency,
                'original_amount' => $amount,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get real-time crypto conversion data
     */
    public function getCryptoConversionData(float $amount, string $fiatCurrency, string $cryptoCurrency): array
    {
        return $this->currencyService->convertFiatToCrypto($amount, $fiatCurrency, $cryptoCurrency);
    }

    /**
     * Get supported currencies
     */
    public function getSupportedFiatCurrencies(): array
    {
        return $this->currencyService->getSupportedFiatCurrencies();
    }

    public function getSupportedCryptoCurrencies(): array
    {
        return $this->currencyService->getSupportedCryptoCurrencies();
    }

    /**
     * Check payment status and update if confirmed
     */
    public function checkPaymentStatus(Payment $payment): bool
    {
        try {
            if ($payment->isConfirmed()) {
                return true;
            }

            if ($payment->isExpired()) {
                $this->markPaymentExpired($payment);
                return false;
            }

            // Check wallet balance
            $wallet = $payment->wallet;
            if (!$wallet) {
                Log::warning('Payment has no associated wallet', [
                    'payment_id' => $payment->payment_id
                ]);
                return false;
            }

            // Get current balance from blockchain
            $balance = $this->walletService->checkWalletBalance($wallet);
            $requiredAmount = $this->walletService->toSmallestUnit(
                (string)$payment->amount,
                $payment->crypto_currency
            );

            Log::debug('Checking payment balance', [
                'payment_id' => $payment->payment_id,
                'wallet_address' => $wallet->address,
                'currency' => $payment->crypto_currency,
                'balance' => $balance,
                'required_amount' => $requiredAmount
            ]);

            // Check if sufficient funds have been received
            if (bccomp($balance, $requiredAmount, 0) >= 0) {
                // Sufficient funds received, but we need to find the transaction
                $transactionHash = $this->findPaymentTransaction($wallet, $requiredAmount);

                if ($transactionHash) {
                    // Check if transaction has enough confirmations
                    $confirmationInfo = $this->walletService->web3Service->isTransactionConfirmed($transactionHash);

                    if ($confirmationInfo['confirmed']) {
                        $this->confirmPayment($payment, $transactionHash);
                        return true;
                    } else {
                        Log::info('Payment transaction found but not yet confirmed', [
                            'payment_id' => $payment->payment_id,
                            'transaction_hash' => $transactionHash,
                            'confirmations' => $confirmationInfo['confirmations'],
                            'required' => $confirmationInfo['required']
                        ]);
                    }
                } else {
                    // Balance is sufficient but no specific transaction found
                    // This could happen with multiple small transactions
                    // For now, we'll create a synthetic transaction hash and confirm
                    Log::info('Sufficient balance found without specific transaction', [
                        'payment_id' => $payment->payment_id,
                        'balance' => $balance,
                        'required' => $requiredAmount
                    ]);

                    $syntheticTxHash = 'balance_' . $payment->payment_id . '_' . time();
                    $this->confirmPayment($payment, $syntheticTxHash);
                    return true;
                }
            }

            return false;

        } catch (Exception $e) {
            Log::error('Failed to check payment status', [
                'payment_id' => $payment->payment_id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Find the transaction hash for a payment by analyzing recent transactions
     * This is a simplified implementation - in production you might want to use
     * event logs or transaction history APIs for better accuracy
     */
    private function findPaymentTransaction(Wallet $wallet, string $requiredAmount): ?string
    {
        try {
            // For now, we'll return null and rely on balance checking
            // In a full implementation, you would:
            // 1. Query recent transactions to the wallet address
            // 2. Filter transactions that match the required amount
            // 3. Return the most recent matching transaction hash

            // This could be implemented using:
            // - Polygon/Ethereum transaction history APIs
            // - Event logs from token contracts
            // - Third-party services like Moralis, Alchemy, etc.

            Log::debug('Transaction search not implemented, using balance-based confirmation', [
                'wallet_address' => $wallet->address,
                'required_amount' => $requiredAmount
            ]);

            return null;
        } catch (Exception $e) {
            Log::error('Failed to find payment transaction', [
                'wallet_address' => $wallet->address,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Confirm a payment
     */
    public function confirmPayment(Payment $payment, string $transactionHash): void
    {
        try {
            // Get blockchain confirmation data if it's a real transaction
            $blockchainData = [];
            if (strpos($transactionHash, 'balance_') !== 0) {
                // Real transaction hash, get confirmation info
                $confirmationInfo = $this->walletService->web3Service->isTransactionConfirmed($transactionHash);
                $blockchainData = [
                    'confirmations' => $confirmationInfo['confirmations'] ?? 0,
                    'block_number' => $confirmationInfo['tx_block'] ?? null,
                    'confirmed_at_block' => $confirmationInfo['current_block'] ?? null,
                    'confirmation_timestamp' => now()->toISOString(),
                ];
            } else {
                // Balance-based confirmation
                $blockchainData = [
                    'confirmation_type' => 'balance_based',
                    'confirmation_timestamp' => now()->toISOString(),
                ];
            }

            $payment->update([
                'status' => 'confirmed',
                'transaction_hash' => $transactionHash,
                'confirmed_at' => now(),
                'blockchain_data' => $blockchainData,
            ]);

            // Update order status
            $order = $payment->order;
            $order->update(['status' => 'processing']);

            // Process digital product delivery
            if ($order->hasDigitalProducts()) {
                $deliveryResult = $this->digitalDeliveryService->processOrderDelivery($order);

                if (!empty($deliveryResult['delivered_items'])) {
                    Log::info('Digital products delivered', [
                        'order_id' => $order->id,
                        'delivered_count' => count($deliveryResult['delivered_items'])
                    ]);
                }

                if (!empty($deliveryResult['errors'])) {
                    Log::error('Some digital products failed to deliver', [
                        'order_id' => $order->id,
                        'errors' => $deliveryResult['errors']
                    ]);
                }
            }

            // Transfer funds to admin wallet (for demo, just log it)
            $wallet = $payment->wallet;
            if ($wallet) {
                Log::info('Would transfer funds to admin wallet', [
                    'payment_id' => $payment->payment_id,
                    'amount' => $payment->amount,
                    'currency' => $payment->crypto_currency,
                    'from_wallet' => $wallet->address
                ]);
            }

            Log::info('Payment confirmed', [
                'payment_id' => $payment->payment_id,
                'order_id' => $order->id,
                'transaction_hash' => $transactionHash
            ]);

        } catch (Exception $e) {
            Log::error('Failed to confirm payment', [
                'payment_id' => $payment->payment_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Mark payment as expired
     */
    public function markPaymentExpired(Payment $payment): void
    {
        try {
            $payment->update(['status' => 'expired']);

            // Mark associated wallet as expired
            $wallet = $payment->wallet;
            if ($wallet && $wallet->status === 'active') {
                $wallet->update(['status' => 'expired']);
            }

            Log::info('Payment marked as expired', [
                'payment_id' => $payment->payment_id,
                'order_id' => $payment->order_id
            ]);

        } catch (Exception $e) {
            Log::error('Failed to mark payment as expired', [
                'payment_id' => $payment->payment_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get pending payments that need status checking
     */
    public function getPendingPayments(): \Illuminate\Database\Eloquent\Collection
    {
        return Payment::where('status', 'pending')
            ->where('expires_at', '>', now())
            ->with(['order', 'wallet'])
            ->get();
    }

    /**
     * Process expired payments
     */
    public function processExpiredPayments(): int
    {
        $expiredPayments = Payment::where('status', 'pending')
            ->where('expires_at', '<=', now())
            ->get();

        $count = 0;
        foreach ($expiredPayments as $payment) {
            $this->markPaymentExpired($payment);
            $count++;
        }

        if ($count > 0) {
            Log::info("Processed {$count} expired payments");
        }

        return $count;
    }

    /**
     * Monitor all pending payments and update their statuses
     */
    public function monitorPayments(): array
    {
        $results = [
            'checked_payments' => 0,
            'confirmed_payments' => 0,
            'expired_payments' => 0,
            'errors' => []
        ];

        try {
            // Get all pending payments
            $pendingPayments = $this->getPendingPayments();
            $results['checked_payments'] = $pendingPayments->count();

            foreach ($pendingPayments as $payment) {
                try {
                    $wasConfirmed = $this->checkPaymentStatus($payment);
                    if ($wasConfirmed) {
                        $results['confirmed_payments']++;
                    }
                } catch (Exception $e) {
                    $results['errors'][] = [
                        'payment_id' => $payment->payment_id,
                        'error' => $e->getMessage()
                    ];
                    Log::error('Error monitoring payment', [
                        'payment_id' => $payment->payment_id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Process expired payments
            $expiredCount = $this->processExpiredPayments();
            $results['expired_payments'] = $expiredCount;

            if ($results['confirmed_payments'] > 0 || $results['expired_payments'] > 0) {
                Log::info('Payment monitoring completed', $results);
            }

        } catch (Exception $e) {
            Log::error('Failed to monitor payments', [
                'error' => $e->getMessage()
            ]);
            $results['errors'][] = [
                'general_error' => $e->getMessage()
            ];
        }

        return $results;
    }

    /**
     * Get payment statistics
     */
    public function getPaymentStats(): array
    {
        return [
            'total_payments' => Payment::count(),
            'confirmed_payments' => Payment::where('status', 'confirmed')->count(),
            'pending_payments' => Payment::where('status', 'pending')->count(),
            'expired_payments' => Payment::where('status', 'expired')->count(),
            'total_volume_usd' => Payment::where('status', 'confirmed')->sum('amount_usd'),
        ];
    }
}
