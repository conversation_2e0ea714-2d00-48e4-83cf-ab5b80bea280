<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DigitalProduct extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'digital_type',
        'file_path',
        'content',
        'account_data',
        'total_quantity',
        'available_quantity',
        'is_unlimited',
        'download_limit',
        'download_expiry_days',
    ];

    protected $casts = [
        'account_data' => 'array',
        'is_unlimited' => 'boolean',
    ];

    /**
     * Get the product that owns the digital product
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Check if digital product is available
     */
    public function isAvailable(): bool
    {
        if ($this->is_unlimited) {
            return true;
        }
        return $this->available_quantity > 0;
    }

    /**
     * Decrease available quantity
     */
    public function decreaseQuantity(int $amount = 1): void
    {
        if (!$this->is_unlimited) {
            $this->decrement('available_quantity', $amount);
        }
    }
}
