@extends('layouts.admin')

@section('title', 'Create Category')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Create Category</h1>
        <a href="{{ route('admin.categories.index') }}" 
           class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
            ← Back to Categories
        </a>
    </div>

    @if($errors->any())
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <ul>
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {{ session('error') }}
        </div>
    @endif

    <form action="{{ route('admin.categories.store') }}" method="POST" class="space-y-6">
        @csrf
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">📂 Category Information</h2>
            
            <div class="space-y-4">
                <!-- Category Name -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Category Name *
                    </label>
                    <input type="text" name="name" value="{{ old('name') }}" required
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                           placeholder="Enter category name">
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        The category name will be used for navigation and product organization.
                    </p>
                </div>

                <!-- Description -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Description
                    </label>
                    <textarea name="description" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                              placeholder="Enter category description (optional)">{{ old('description') }}</textarea>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        Optional description to help customers understand what products are in this category.
                    </p>
                </div>

                <!-- Status -->
                <div class="flex items-center">
                    <input type="checkbox" name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Category is Active
                    </label>
                </div>
                <p class="text-xs text-gray-600 dark:text-gray-400">
                    Active categories will be visible to customers and available for product assignment.
                </p>
            </div>
        </div>

        <!-- Category Guidelines -->
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
            <h3 class="text-lg font-medium text-blue-900 dark:text-blue-200 mb-3">💡 Category Guidelines</h3>
            <ul class="space-y-2 text-sm text-blue-800 dark:text-blue-300">
                <li>• Choose clear, descriptive names that customers will easily understand</li>
                <li>• Keep category names concise but specific (e.g., "Electronics" rather than "Electronic Items and Gadgets")</li>
                <li>• Use consistent naming conventions across all categories</li>
                <li>• Consider how categories will appear in navigation menus</li>
                <li>• Categories can be deactivated later if needed without deleting them</li>
            </ul>
        </div>

        <!-- Submit Buttons -->
        <div class="flex justify-end space-x-4">
            <a href="{{ route('admin.categories.index') }}" 
               class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-6 rounded-lg transition duration-200">
                Cancel
            </a>
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition duration-200">
                📂 Create Category
            </button>
        </div>
    </form>
</div>
@endsection
