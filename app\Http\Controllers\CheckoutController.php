<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\CheckoutRequest;
use App\Models\Product;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Services\PaymentService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CheckoutController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * Show checkout page
     */
    public function index(Request $request)
    {
        $productId = $request->get('product_id');
        $quantity = $request->get('quantity', 1);

        if (!$productId) {
            return redirect()->route('shop.index')->with('error', 'No product selected for checkout.');
        }

        $product = Product::active()->findOrFail($productId);

        if (!$product->inStock() && $product->track_stock) {
            return redirect()->route('shop.product', $product->slug)
                ->with('error', 'Product is out of stock.');
        }

        $subtotal = $product->price * $quantity;
        $tax = 0; // No tax for demo
        $shipping = $product->isPhysical() ? 10.00 : 0; // $10 shipping for physical products
        $total = $subtotal + $tax + $shipping;

        return view('checkout.index', compact('product', 'quantity', 'subtotal', 'tax', 'shipping', 'total'));
    }

    /**
     * Process checkout
     */
    public function store(CheckoutRequest $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'crypto_currency' => 'required|in:USDT,POL',
            'billing_address' => 'nullable|array',
            'shipping_address' => 'nullable|array',
        ]);

        try {
            DB::beginTransaction();

            $product = Product::findOrFail($request->product_id);
            $quantity = $request->quantity;

            // Check stock
            if ($product->track_stock && $product->stock_quantity < $quantity) {
                throw new \Exception('Insufficient stock available.');
            }

            // Calculate totals
            $subtotal = $product->price * $quantity;
            $shipping = $product->isPhysical() ? 10.00 : 0;
            $total = $subtotal + $shipping;

            // Create order
            $order = Order::create([
                'user_id' => auth()->id(),
                'customer_name' => $request->customer_name,
                'customer_email' => $request->customer_email,
                'customer_phone' => $request->customer_phone,
                'status' => 'pending',
                'subtotal' => $subtotal,
                'shipping_amount' => $shipping,
                'total_amount' => $total,
                'currency' => $product->base_currency ?? 'USD',
                'original_currency' => $product->base_currency ?? 'USD',
                'original_amount' => $total,
                'fiat_to_usd_rate' => 1.0, // Will be updated by PaymentService if needed
                'exchange_rate_timestamp' => now(),
                'billing_address' => $request->billing_address,
                'shipping_address' => $request->shipping_address,
            ]);

            // Create order item
            OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $product->id,
                'product_name' => $product->name,
                'product_sku' => $product->sku,
                'price' => $product->price,
                'quantity' => $quantity,
                'total' => $subtotal,
                'product_data' => [
                    'type' => $product->type,
                    'description' => $product->description,
                ],
            ]);

            // Update stock
            if ($product->track_stock) {
                $product->decrement('stock_quantity', $quantity);
            }

            // Create payment using product's base currency
            $payment = $this->paymentService->createPayment(
                $order,
                $request->crypto_currency,
                $total,
                $product->base_currency ?? 'USD'
            );

            DB::commit();

            return redirect()->route('checkout.payment', $payment->payment_id);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Checkout failed', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return back()->withInput()->with('error', 'Checkout failed: ' . $e->getMessage());
        }
    }

    /**
     * Show payment page
     */
    public function payment(Payment $payment)
    {
        if ($payment->isExpired()) {
            return redirect()->route('shop.index')->with('error', 'Payment has expired.');
        }

        $payment->load(['order.items.product', 'wallet']);

        return view('checkout.payment', compact('payment'));
    }

    /**
     * Show success page
     */
    public function success(Order $order)
    {
        if (!$order->isPaid()) {
            return redirect()->route('shop.index')->with('error', 'Order payment not confirmed.');
        }

        $order->load(['items.product', 'payment']);

        return view('checkout.success', compact('order'));
    }
}
