<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'short_description',
        'type',
        'price',
        'compare_price',
        'base_currency',
        'stock_quantity',
        'track_stock',
        'is_active',
        'is_featured',
        'sku',
        'weight',
        'dimensions',
        'category_id',
        'meta_data',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'compare_price' => 'decimal:2',
        'weight' => 'decimal:2',
        'dimensions' => 'array',
        'meta_data' => 'array',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'track_stock' => 'boolean',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
            if (empty($product->sku)) {
                $product->sku = 'PRD-' . strtoupper(Str::random(8));
            }
        });
    }

    /**
     * Get the category that owns the product
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the product images
     */
    public function images()
    {
        return $this->hasMany(ProductImage::class)->orderBy('sort_order');
    }

    /**
     * Get the primary image
     */
    public function primaryImage()
    {
        return $this->hasOne(ProductImage::class)->where('is_primary', true);
    }

    /**
     * Get the digital product data
     */
    public function digitalProduct()
    {
        return $this->hasOne(DigitalProduct::class);
    }

    /**
     * Get order items for this product
     */
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Check if product is digital
     */
    public function isDigital(): bool
    {
        return $this->type === 'digital';
    }

    /**
     * Check if product is physical
     */
    public function isPhysical(): bool
    {
        return $this->type === 'physical';
    }

    /**
     * Check if product is in stock
     */
    public function inStock(): bool
    {
        if (!$this->track_stock) {
            return true;
        }
        return $this->stock_quantity > 0;
    }

    /**
     * Get formatted price in the product's base currency
     */
    public function getFormattedPrice(): string
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'CAD' => 'C$',
            'AUD' => 'A$',
            'JPY' => '¥',
        ];

        $symbol = $symbols[$this->base_currency] ?? $this->base_currency;
        $decimals = $this->base_currency === 'JPY' ? 0 : 2;

        return $symbol . number_format($this->price, $decimals);
    }

    /**
     * Get formatted compare price in the product's base currency
     */
    public function getFormattedComparePrice(): string
    {
        if (!$this->compare_price) {
            return '';
        }

        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'CAD' => 'C$',
            'AUD' => 'A$',
            'JPY' => '¥',
        ];

        $symbol = $symbols[$this->base_currency] ?? $this->base_currency;
        $decimals = $this->base_currency === 'JPY' ? 0 : 2;

        return $symbol . number_format($this->compare_price, $decimals);
    }

    /**
     * Get savings amount in formatted currency
     */
    public function getFormattedSavings(): string
    {
        if (!$this->compare_price || $this->compare_price <= $this->price) {
            return '';
        }

        $savings = $this->compare_price - $this->price;
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'CAD' => 'C$',
            'AUD' => 'A$',
            'JPY' => '¥',
        ];

        $symbol = $symbols[$this->base_currency] ?? $this->base_currency;
        $decimals = $this->base_currency === 'JPY' ? 0 : 2;

        return $symbol . number_format($savings, $decimals);
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute(): string
    {
        return '$' . number_format($this->price, 2);
    }

    /**
     * Scope for active products
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for featured products
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for digital products
     */
    public function scopeDigital($query)
    {
        return $query->where('type', 'digital');
    }

    /**
     * Scope for physical products
     */
    public function scopePhysical($query)
    {
        return $query->where('type', 'physical');
    }
}
