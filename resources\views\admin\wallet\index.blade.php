@extends('layouts.admin')

@section('title', 'Wallet Management')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Wallet Management</h2>
        <div class="flex space-x-3">
            @if($hasAdminWallet)
                <a href="{{ route('admin.wallet.create') }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                    ⚙️ Update Admin Wallet
                </a>
            @else
                <a href="{{ route('admin.wallet.create') }}" 
                   class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                    ➕ Set Admin Wallet
                </a>
            @endif
            <form action="{{ route('admin.wallet.cleanup') }}" method="POST" class="inline">
                @csrf
                <button type="submit" 
                        class="bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200"
                        onclick="return confirm('This will clean up expired and old wallets. Continue?')">
                    🧹 Cleanup Wallets
                </button>
            </form>
        </div>
    </div>

    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {{ session('error') }}
        </div>
    @endif

    <!-- Admin Wallet Status -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">🏦 Admin Wallet Configuration</h3>
        
        @if($hasAdminWallet)
            <div class="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div>
                    <div class="flex items-center space-x-2 mb-2">
                        <span class="text-green-600 dark:text-green-400 text-2xl">✅</span>
                        <span class="text-lg font-medium text-green-900 dark:text-green-200">Admin Wallet Configured</span>
                    </div>
                    <div class="text-sm text-green-800 dark:text-green-300">
                        <strong>Address:</strong> 
                        <code class="bg-green-100 dark:bg-green-800 px-2 py-1 rounded text-xs">{{ $adminWalletAddress }}</code>
                    </div>
                    <p class="text-xs text-green-700 dark:text-green-400 mt-1">
                        All payments will be transferred to this wallet address.
                    </p>
                </div>
                <div class="flex space-x-2">
                    @foreach($supportedCurrencies as $currency)
                        <button onclick="checkAdminBalance('{{ $currency }}')" 
                                class="bg-green-600 hover:bg-green-700 text-white text-xs font-medium py-1 px-3 rounded transition duration-200">
                            Check {{ $currency }} Balance
                        </button>
                    @endforeach
                </div>
            </div>
        @else
            <div class="flex items-center justify-between p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <div>
                    <div class="flex items-center space-x-2 mb-2">
                        <span class="text-red-600 dark:text-red-400 text-2xl">⚠️</span>
                        <span class="text-lg font-medium text-red-900 dark:text-red-200">Admin Wallet Not Configured</span>
                    </div>
                    <p class="text-sm text-red-800 dark:text-red-300">
                        You need to set up an admin wallet to receive payments from customers.
                    </p>
                </div>
                <a href="{{ route('admin.wallet.create') }}" 
                   class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                    Set Up Now
                </a>
            </div>
        @endif
    </div>

    <!-- Wallet Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <span class="text-blue-600 dark:text-blue-400 text-sm font-bold">💼</span>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Wallets</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($stats['total_wallets']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                        <span class="text-green-600 dark:text-green-400 text-sm font-bold">✅</span>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Active Wallets</p>
                    <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ number_format($stats['active_wallets']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                        <span class="text-purple-600 dark:text-purple-400 text-sm font-bold">✔️</span>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Used Wallets</p>
                    <p class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ number_format($stats['used_wallets']) }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                        <span class="text-red-600 dark:text-red-400 text-sm font-bold">⏰</span>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Expired Wallets</p>
                    <p class="text-2xl font-bold text-red-600 dark:text-red-400">{{ number_format($stats['expired_wallets']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Wallets -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Wallets</h3>
        </div>

        @if($recentWallets->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Wallet Address
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Currency
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Payment
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Created
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach($recentWallets as $wallet)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-mono text-gray-900 dark:text-white">
                                        {{ Str::limit($wallet->address, 20, '...') }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                        {{ $wallet->currency }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        @switch($wallet->status)
                                            @case('active') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 @break
                                            @case('used') bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 @break
                                            @case('expired') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @break
                                            @default bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                                        @endswitch">
                                        {{ ucfirst($wallet->status) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    @if($wallet->payment)
                                        <div>
                                            <div class="font-medium">{{ $wallet->payment->payment_id }}</div>
                                            @if($wallet->payment->order)
                                                <div class="text-xs">Order #{{ $wallet->payment->order->order_number }}</div>
                                            @endif
                                        </div>
                                    @else
                                        <span class="text-gray-400">No payment</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    {{ $wallet->created_at->format('M j, Y g:i A') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{{ route('admin.wallet.show', $wallet) }}" 
                                       class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                                        View Details
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-12">
                <div class="text-gray-500 dark:text-gray-400 text-lg mb-4">
                    No wallets created yet.
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    Wallets will appear here when customers make payments.
                </p>
            </div>
        @endif
    </div>
</div>

<!-- Balance Check Modal -->
<div id="balanceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Wallet Balance</h3>
            <button onclick="closeBalanceModal()" class="text-gray-400 hover:text-gray-600">
                <span class="sr-only">Close</span>
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div id="balanceContent">
            <div class="text-center py-4">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">Checking balance...</p>
            </div>
        </div>
    </div>
</div>

<script>
function checkAdminBalance(currency) {
    const modal = document.getElementById('balanceModal');
    const content = document.getElementById('balanceContent');
    
    modal.classList.remove('hidden');
    modal.classList.add('flex');
    
    // Reset content
    content.innerHTML = `
        <div class="text-center py-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">Checking ${currency} balance...</p>
        </div>
    `;
    
    fetch('{{ route("admin.wallet.check-balance") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            address: '{{ $adminWalletAddress }}',
            currency: currency
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            content.innerHTML = `
                <div class="text-center py-4">
                    <div class="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">
                        ${data.formatted_balance}
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Current balance for ${currency}
                    </p>
                </div>
            `;
        } else {
            content.innerHTML = `
                <div class="text-center py-4">
                    <div class="text-red-600 dark:text-red-400 mb-2">
                        <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <p class="text-sm text-red-600 dark:text-red-400">
                        ${data.message}
                    </p>
                </div>
            `;
        }
    })
    .catch(error => {
        content.innerHTML = `
            <div class="text-center py-4">
                <div class="text-red-600 dark:text-red-400 mb-2">
                    <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <p class="text-sm text-red-600 dark:text-red-400">
                    Failed to check balance
                </p>
            </div>
        `;
    });
}

function closeBalanceModal() {
    const modal = document.getElementById('balanceModal');
    modal.classList.add('hidden');
    modal.classList.remove('flex');
}
</script>
@endsection
