<?php

namespace App\Services;

use App\Models\Wallet;
use App\Models\Payment;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Exception;

class WalletService
{
    protected $web3Service;

    public function __construct(Web3Service $web3Service)
    {
        $this->web3Service = $web3Service;
    }

    /**
     * Generate wallet data using Web3Service
     */
    public function generateWallet(): array
    {
        return $this->web3Service->generateWallet();
    }

    /**
     * Create wallet record for a payment
     */
    public function createWalletRecord(Payment $payment, array $walletData, string $currency): Wallet
    {
        try {
            // Create wallet record
            $wallet = Wallet::create([
                'address' => $walletData['address'],
                'private_key' => Crypt::encryptString($walletData['private_key']),
                'currency' => $currency,
                'status' => 'active',
                'payment_id' => $payment->id,
                'expires_at' => now()->addMinutes((int) config('web3.payment_timeout_minutes', 30)),
            ]);

            Log::info('Created wallet record for payment', [
                'payment_id' => $payment->id,
                'wallet_address' => $wallet->address,
                'currency' => $currency
            ]);

            return $wallet;

        } catch (Exception $e) {
            Log::error('Failed to create wallet record for payment', [
                'payment_id' => $payment->id,
                'currency' => $currency,
                'error' => $e->getMessage()
            ]);
            throw new Exception('Failed to create wallet record');
        }
    }

    /**
     * Generate a new wallet for a payment (legacy method)
     */
    public function generateWalletForPayment(Payment $payment, string $currency): Wallet
    {
        try {
            // For demo purposes, generate a mock wallet
            $walletData = $this->generateMockWallet();
            
            // Create wallet record
            $wallet = Wallet::create([
                'address' => $walletData['address'],
                'private_key' => Crypt::encryptString($walletData['private_key']),
                'currency' => $currency,
                'status' => 'active',
                'payment_id' => $payment->id,
                'expires_at' => now()->addMinutes((int) config('web3.payment_timeout_minutes', 30)),
            ]);

            Log::info('Generated wallet for payment', [
                'payment_id' => $payment->id,
                'wallet_address' => $wallet->address,
                'currency' => $currency
            ]);

            return $wallet;

        } catch (Exception $e) {
            Log::error('Failed to generate wallet for payment', [
                'payment_id' => $payment->id,
                'currency' => $currency,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Generate mock wallet for demo purposes
     */
    private function generateMockWallet(): array
    {
        // Generate a mock Ethereum-style address
        $address = '0x' . bin2hex(random_bytes(20));
        $privateKey = bin2hex(random_bytes(32));

        return [
            'address' => $address,
            'private_key' => $privateKey
        ];
    }

    /**
     * Check wallet balance
     */
    public function checkWalletBalance(Wallet $wallet): string
    {
        try {
            $contractAddress = $this->getTokenContractAddress($wallet->currency);
            
            if ($wallet->currency === 'MATIC') {
                return $this->web3Service->getEthBalance($wallet->address);
            } else {
                return $this->web3Service->getTokenBalance($wallet->address, $contractAddress);
            }
        } catch (Exception $e) {
            Log::error('Failed to check wallet balance', [
                'wallet_id' => $wallet->id,
                'address' => $wallet->address,
                'error' => $e->getMessage()
            ]);
            return '0';
        }
    }

    /**
     * Transfer funds from wallet to admin wallet
     */
    public function transferToAdminWallet(Wallet $wallet, string $amount): array
    {
        try {
            $adminWalletAddress = config('web3.admin_wallet_address');
            
            if (empty($adminWalletAddress)) {
                throw new Exception('Admin wallet address not configured');
            }

            $privateKey = Crypt::decryptString($wallet->private_key);
            $contractAddress = $this->getTokenContractAddress($wallet->currency);

            $result = $this->web3Service->transferTokens(
                $privateKey,
                $adminWalletAddress,
                $amount,
                $contractAddress
            );

            if ($result['success']) {
                // Update wallet status
                $wallet->update([
                    'status' => 'used',
                    'used_at' => now()
                ]);

                Log::info('Successfully transferred funds to admin wallet', [
                    'wallet_id' => $wallet->id,
                    'amount' => $amount,
                    'currency' => $wallet->currency,
                    'tx_hash' => $result['transaction_hash']
                ]);
            }

            return $result;

        } catch (Exception $e) {
            Log::error('Failed to transfer funds to admin wallet', [
                'wallet_id' => $wallet->id,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get token contract address based on currency and network
     */
    private function getTokenContractAddress(string $currency): string
    {
        $network = config('web3.use_testnet') ? 'testnet' : 'mainnet';
        $contracts = config('web3.contracts.' . $network);
        
        $currencyKey = strtolower($currency);
        
        if (!isset($contracts[$currencyKey])) {
            throw new Exception("Contract address not found for currency: {$currency}");
        }

        return $contracts[$currencyKey];
    }

    /**
     * Convert token amount to smallest unit (considering decimals)
     */
    public function toSmallestUnit(string $amount, string $currency): string
    {
        $decimals = config('web3.token_decimals.' . strtoupper($currency), 18);
        return bcmul($amount, bcpow('10', $decimals));
    }

    /**
     * Convert from smallest unit to human readable amount
     */
    public function fromSmallestUnit(string $amount, string $currency): string
    {
        $decimals = config('web3.token_decimals.' . strtoupper($currency), 18);
        return bcdiv($amount, bcpow('10', $decimals), $decimals);
    }

    /**
     * Get all active wallets that need monitoring
     */
    public function getActiveWallets(): \Illuminate\Database\Eloquent\Collection
    {
        return Wallet::where('status', 'active')
            ->where('expires_at', '>', now())
            ->with('payment.order')
            ->get();
    }

    /**
     * Mark expired wallets
     */
    public function markExpiredWallets(): int
    {
        $count = Wallet::where('status', 'active')
            ->where('expires_at', '<=', now())
            ->update(['status' => 'expired']);

        if ($count > 0) {
            Log::info("Marked {$count} wallets as expired");
        }

        return $count;
    }

    /**
     * Clean up old wallets
     */
    public function cleanupOldWallets(int $daysOld = 30): int
    {
        $count = Wallet::where('created_at', '<', now()->subDays($daysOld))
            ->whereIn('status', ['used', 'expired'])
            ->delete();

        if ($count > 0) {
            Log::info("Cleaned up {$count} old wallets");
        }

        return $count;
    }
}
