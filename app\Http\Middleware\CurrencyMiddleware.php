<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CurrencyMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if currency is being changed via request
        if ($request->has('currency')) {
            $currency = strtoupper($request->get('currency'));
            $supportedCurrencies = config('currency.supported_fiat');

            if (in_array($currency, $supportedCurrencies)) {
                session(['selected_currency' => $currency]);
            }
        }

        // Set default currency if none is selected
        if (!session()->has('selected_currency')) {
            session(['selected_currency' => config('currency.default_currency', 'USD')]);
        }

        // Make currency available globally
        view()->share('selectedCurrency', session('selected_currency'));

        return $next($request);
    }
}
