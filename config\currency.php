<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Currency API Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for cryptocurrency and fiat currency exchange rate APIs
    |
    */

    'crypto_api' => [
        'provider' => env('CRYPTO_API_PROVIDER', 'coingecko'),
        'coingecko' => [
            'base_url' => 'https://api.coingecko.com/api/v3',
            'api_key' => env('COINGECKO_API_KEY'), // Optional for free tier
            'rate_limit' => 50, // requests per minute for free tier
        ],
        'cryptocompare' => [
            'base_url' => 'https://min-api.cryptocompare.com/data',
            'api_key' => env('CRYPTOCOMPARE_API_KEY'),
            'rate_limit' => 100,
        ],
    ],

    'fiat_api' => [
        'provider' => env('FIAT_API_PROVIDER', 'exchangerate-api'),
        'exchangerate-api' => [
            'base_url' => 'https://api.exchangerate-api.com/v4',
            'api_key' => env('EXCHANGERATE_API_KEY'), // Optional for free tier
            'rate_limit' => 1500, // requests per month for free tier
        ],
        'fixer' => [
            'base_url' => 'http://data.fixer.io/api',
            'api_key' => env('FIXER_API_KEY'),
            'rate_limit' => 100,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Supported Currencies
    |--------------------------------------------------------------------------
    |
    | List of supported fiat and cryptocurrency currencies
    |
    */

    'supported_fiat' => ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'],
    'supported_crypto' => ['USDT', 'POL'],

    /*
    |--------------------------------------------------------------------------
    | Default Currency
    |--------------------------------------------------------------------------
    |
    | The default currency for the application
    |
    */

    'default_currency' => env('DEFAULT_CURRENCY', 'USD'),

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    | Cache duration for exchange rates and crypto prices
    |
    */

    'cache' => [
        'crypto_prices_ttl' => 300, // 5 minutes
        'fiat_rates_ttl' => 3600, // 1 hour
        'fallback_ttl' => 86400, // 24 hours for fallback rates
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Validation
    |--------------------------------------------------------------------------
    |
    | Settings for validating exchange rates to prevent extreme fluctuations
    |
    */

    'validation' => [
        'max_rate_change_percent' => 50, // Maximum allowed rate change
        'min_rate' => 0.000001,
        'max_rate' => 1000000,
    ],

    /*
    |--------------------------------------------------------------------------
    | Fallback Rates
    |--------------------------------------------------------------------------
    |
    | Fallback exchange rates when APIs are unavailable
    |
    */

    'fallback_rates' => [
        'fiat' => [
            'USD' => 1.0,
            'EUR' => 0.85,
            'GBP' => 0.73,
            'CAD' => 1.35,
            'AUD' => 1.50,
            'JPY' => 110.0,
        ],
        'crypto' => [
            'USDT' => 1.0, // USD
            'POL' => 0.5,  // USD
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Currency Display Settings
    |--------------------------------------------------------------------------
    |
    | Settings for displaying currencies in the frontend
    |
    */

    'display' => [
        'decimal_places' => [
            'USD' => 2,
            'EUR' => 2,
            'GBP' => 2,
            'CAD' => 2,
            'AUD' => 2,
            'JPY' => 0,
        ],
        'symbols' => [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'CAD' => 'C$',
            'AUD' => 'A$',
            'JPY' => '¥',
        ],
    ],
];
