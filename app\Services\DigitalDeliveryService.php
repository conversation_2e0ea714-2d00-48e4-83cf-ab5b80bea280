<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\DigitalProduct;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Exception;

class DigitalDeliveryService
{
    /**
     * Process digital product delivery for an order
     */
    public function processOrderDelivery(Order $order): array
    {
        $deliveredItems = [];
        $errors = [];

        foreach ($order->items as $item) {
            if ($item->product->isDigital() && !$item->is_delivered) {
                try {
                    $deliveryData = $this->deliverDigitalProduct($item);
                    
                    // Update order item with delivery data
                    $item->update([
                        'digital_delivery_data' => $deliveryData,
                        'is_delivered' => true,
                        'delivered_at' => now(),
                    ]);

                    $deliveredItems[] = $item;

                    Log::info('Digital product delivered', [
                        'order_id' => $order->id,
                        'item_id' => $item->id,
                        'product_id' => $item->product_id,
                    ]);

                } catch (Exception $e) {
                    $errors[] = [
                        'item_id' => $item->id,
                        'product_name' => $item->product_name,
                        'error' => $e->getMessage(),
                    ];

                    Log::error('Failed to deliver digital product', [
                        'order_id' => $order->id,
                        'item_id' => $item->id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        }

        return [
            'delivered_items' => $deliveredItems,
            'errors' => $errors,
        ];
    }

    /**
     * Deliver a specific digital product
     */
    public function deliverDigitalProduct(OrderItem $orderItem): array
    {
        $product = $orderItem->product;
        $digitalProduct = $product->digitalProduct;

        if (!$digitalProduct) {
            throw new Exception('Digital product data not found');
        }

        switch ($digitalProduct->digital_type) {
            case 'file':
                return $this->deliverFile($digitalProduct, $orderItem);
            
            case 'text':
                return $this->deliverText($digitalProduct, $orderItem);
            
            case 'account_credentials':
                return $this->deliverAccountCredentials($digitalProduct, $orderItem);
            
            case 'image':
            case 'audio':
                return $this->deliverMediaFile($digitalProduct, $orderItem);
            
            default:
                return $this->deliverGeneric($digitalProduct, $orderItem);
        }
    }

    /**
     * Deliver file-based digital product
     */
    private function deliverFile(DigitalProduct $digitalProduct, OrderItem $orderItem): array
    {
        if (!$digitalProduct->file_path) {
            throw new Exception('File path not specified for digital product');
        }

        // Check if file exists
        if (!Storage::exists($digitalProduct->file_path)) {
            throw new Exception('Digital product file not found');
        }

        // Generate secure download link
        $downloadToken = $this->generateDownloadToken($orderItem);
        
        return [
            'type' => 'file',
            'download_token' => $downloadToken,
            'download_url' => route('digital.download', $downloadToken),
            'file_name' => basename($digitalProduct->file_path),
            'download_limit' => $digitalProduct->download_limit,
            'expires_at' => $digitalProduct->download_expiry_days 
                ? now()->addDays($digitalProduct->download_expiry_days) 
                : null,
            'downloads_remaining' => $digitalProduct->download_limit,
        ];
    }

    /**
     * Deliver text-based digital product
     */
    private function deliverText(DigitalProduct $digitalProduct, OrderItem $orderItem): array
    {
        return [
            'type' => 'text',
            'content' => $digitalProduct->content,
            'delivered_at' => now(),
        ];
    }

    /**
     * Deliver account credentials
     */
    private function deliverAccountCredentials(DigitalProduct $digitalProduct, OrderItem $orderItem): array
    {
        $accountData = $digitalProduct->account_data;
        
        if (!$accountData || !isset($accountData['accounts'])) {
            throw new Exception('No account credentials available');
        }

        $availableAccounts = collect($accountData['accounts']);
        
        if ($availableAccounts->isEmpty()) {
            throw new Exception('No account credentials available');
        }

        // Get the required number of accounts based on quantity
        $quantity = $orderItem->quantity;
        $accountsToDeliver = $availableAccounts->take($quantity);

        if ($accountsToDeliver->count() < $quantity) {
            throw new Exception('Insufficient account credentials available');
        }

        // Remove delivered accounts from available pool
        $remainingAccounts = $availableAccounts->skip($quantity)->values();
        
        // Update digital product with remaining accounts
        $digitalProduct->update([
            'account_data' => [
                'accounts' => $remainingAccounts->toArray()
            ],
            'available_quantity' => $digitalProduct->available_quantity - $quantity,
        ]);

        return [
            'type' => 'account_credentials',
            'accounts' => $accountsToDeliver->toArray(),
            'quantity' => $quantity,
            'delivered_at' => now(),
        ];
    }

    /**
     * Deliver media file (image, audio)
     */
    private function deliverMediaFile(DigitalProduct $digitalProduct, OrderItem $orderItem): array
    {
        if (!$digitalProduct->file_path) {
            throw new Exception('Media file path not specified');
        }

        if (!Storage::exists($digitalProduct->file_path)) {
            throw new Exception('Media file not found');
        }

        $downloadToken = $this->generateDownloadToken($orderItem);
        
        return [
            'type' => $digitalProduct->digital_type,
            'download_token' => $downloadToken,
            'download_url' => route('digital.download', $downloadToken),
            'file_name' => basename($digitalProduct->file_path),
            'download_limit' => $digitalProduct->download_limit,
            'expires_at' => $digitalProduct->download_expiry_days 
                ? now()->addDays($digitalProduct->download_expiry_days) 
                : null,
            'downloads_remaining' => $digitalProduct->download_limit,
        ];
    }

    /**
     * Deliver generic digital product
     */
    private function deliverGeneric(DigitalProduct $digitalProduct, OrderItem $orderItem): array
    {
        $deliveryData = [
            'type' => 'generic',
            'delivered_at' => now(),
        ];

        if ($digitalProduct->content) {
            $deliveryData['content'] = $digitalProduct->content;
        }

        if ($digitalProduct->file_path && Storage::exists($digitalProduct->file_path)) {
            $downloadToken = $this->generateDownloadToken($orderItem);
            $deliveryData['download_token'] = $downloadToken;
            $deliveryData['download_url'] = route('digital.download', $downloadToken);
        }

        return $deliveryData;
    }

    /**
     * Generate secure download token
     */
    private function generateDownloadToken(OrderItem $orderItem): string
    {
        $tokenData = [
            'order_item_id' => $orderItem->id,
            'order_id' => $orderItem->order_id,
            'product_id' => $orderItem->product_id,
            'generated_at' => now()->timestamp,
        ];

        return Crypt::encryptString(json_encode($tokenData));
    }

    /**
     * Validate and decode download token
     */
    public function validateDownloadToken(string $token): ?array
    {
        try {
            $decrypted = Crypt::decryptString($token);
            $tokenData = json_decode($decrypted, true);

            if (!$tokenData || !isset($tokenData['order_item_id'])) {
                return null;
            }

            $orderItem = OrderItem::with(['order', 'product.digitalProduct'])
                ->find($tokenData['order_item_id']);

            if (!$orderItem || !$orderItem->is_delivered) {
                return null;
            }

            // Check if download has expired
            $deliveryData = $orderItem->digital_delivery_data;
            if (isset($deliveryData['expires_at']) && $deliveryData['expires_at']) {
                $expiresAt = \Carbon\Carbon::parse($deliveryData['expires_at']);
                if ($expiresAt->isPast()) {
                    return null;
                }
            }

            // Check download limit
            if (isset($deliveryData['downloads_remaining']) && $deliveryData['downloads_remaining'] <= 0) {
                return null;
            }

            return [
                'order_item' => $orderItem,
                'token_data' => $tokenData,
            ];

        } catch (Exception $e) {
            Log::error('Failed to validate download token', [
                'token' => substr($token, 0, 20) . '...',
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Process file download and update download count
     */
    public function processDownload(string $token): array
    {
        $validation = $this->validateDownloadToken($token);
        
        if (!$validation) {
            throw new Exception('Invalid or expired download token');
        }

        $orderItem = $validation['order_item'];
        $digitalProduct = $orderItem->product->digitalProduct;

        if (!$digitalProduct->file_path || !Storage::exists($digitalProduct->file_path)) {
            throw new Exception('File not found');
        }

        // Update download count
        $deliveryData = $orderItem->digital_delivery_data;
        if (isset($deliveryData['downloads_remaining']) && $deliveryData['downloads_remaining'] > 0) {
            $deliveryData['downloads_remaining']--;
            $orderItem->update(['digital_delivery_data' => $deliveryData]);
        }

        return [
            'file_path' => $digitalProduct->file_path,
            'file_name' => basename($digitalProduct->file_path),
            'mime_type' => Storage::mimeType($digitalProduct->file_path),
        ];
    }

    /**
     * Get delivery status for an order
     */
    public function getDeliveryStatus(Order $order): array
    {
        $digitalItems = $order->items->filter(function ($item) {
            return $item->product->isDigital();
        });

        $totalDigitalItems = $digitalItems->count();
        $deliveredItems = $digitalItems->where('is_delivered', true)->count();

        return [
            'total_digital_items' => $totalDigitalItems,
            'delivered_items' => $deliveredItems,
            'pending_items' => $totalDigitalItems - $deliveredItems,
            'is_fully_delivered' => $totalDigitalItems > 0 && $deliveredItems === $totalDigitalItems,
        ];
    }
}
