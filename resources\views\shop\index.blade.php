@extends('layouts.shop')

@section('title', 'PolyPay Store - Cryptocurrency E-commerce')

@php
    use App\Helpers\CurrencyHelper;
@endphp

@section('content')
<div class="min-h-screen">
    <!-- Hero Section -->
    <div class="hero-gradient relative overflow-hidden">
        <div class="absolute inset-0 bg-animated opacity-90"></div>
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="text-center">
                <div class="float mb-8">
                    <div class="inline-flex items-center justify-center w-20 h-20 rounded-full glass mb-6">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                        </svg>
                    </div>
                </div>
                <h1 class="text-5xl md:text-6xl font-bold text-white mb-6 fade-in">
                    Welcome to <span class="bg-gradient-to-r from-yellow-400 to-pink-400 bg-clip-text text-transparent">PolyPay</span>
                </h1>
                <p class="text-xl md:text-2xl text-white/90 mb-10 max-w-3xl mx-auto fade-in">
                    Experience the future of e-commerce with seamless cryptocurrency payments on the Polygon network
                </p>
                <div class="flex flex-wrap justify-center gap-4 mb-10 fade-in">
                    <div class="crypto-badge">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"/>
                        </svg>
                        USDT Payments
                    </div>
                    <div class="crypto-badge">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        POL Network
                    </div>
                    <div class="crypto-badge">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                        </svg>
                        Secure & Fast
                    </div>
                </div>
                <div class="flex flex-col sm:flex-row gap-4 justify-center fade-in">
                    <a href="#products" class="btn-gradient inline-flex items-center justify-center px-8 py-4 text-lg">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                        </svg>
                        Start Shopping
                    </a>
                    <a href="#categories" class="btn-crypto inline-flex items-center justify-center px-8 py-4 text-lg">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        Browse Categories
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl animate-pulse"></div>
        <div class="absolute bottom-20 right-10 w-32 h-32 bg-pink-500/20 rounded-full blur-2xl animate-pulse"></div>
        <div class="absolute top-1/2 left-1/4 w-16 h-16 bg-blue-500/20 rounded-full blur-xl animate-pulse"></div>
    </div>

    <!-- Categories Section -->
    @if($categories->count() > 0)
    <div id="categories" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center mb-12">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4 fade-in">
                Shop by <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Category</span>
            </h2>
            <p class="text-lg text-gray-600 dark:text-gray-300 fade-in">Discover our curated collection of premium products</p>
        </div>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
            @foreach($categories as $category)
            <a href="{{ route('shop.category', $category->slug) }}" class="group fade-in">
                <div class="category-card relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    @if($category->image)
                    <div class="relative z-10">
                        <img src="{{ asset('storage/' . $category->image) }}" alt="{{ $category->name }}" class="w-20 h-20 mx-auto mb-4 rounded-full object-cover shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                    </div>
                    @else
                    <div class="relative z-10">
                        <div class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                            <span class="text-3xl">📦</span>
                        </div>
                    </div>
                    @endif
                    <div class="relative z-10">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                            {{ $category->name }}
                        </h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                            {{ $category->products_count ?? 0 }} products
                        </p>
                    </div>
                    <div class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-purple-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
                </div>
            </a>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Featured Products Section -->
    @if($featuredProducts->count() > 0)
    <div id="products" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 bg-gray-50/50 dark:bg-gray-900/50">
        <div class="text-center mb-12">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4 fade-in">
                Featured <span class="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Products</span>
            </h2>
            <p class="text-lg text-gray-600 dark:text-gray-300 fade-in">Handpicked items just for you</p>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            @foreach($featuredProducts as $product)
            <div class="product-card fade-in group">
                <div class="relative overflow-hidden">
                    <a href="{{ route('shop.product', $product->slug) }}">
                        @if($product->primaryImage)
                        <img src="{{ asset('storage/' . $product->primaryImage->image_path) }}" alt="{{ $product->name }}" class="w-full h-56 object-cover transition-transform duration-500 group-hover:scale-110">
                        @else
                        <div class="w-full h-56 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 flex items-center justify-center">
                            <span class="text-5xl opacity-50">📦</span>
                        </div>
                        @endif
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                    
                    <!-- Product Type Badge -->
                    <div class="absolute top-3 left-3">
                        <span class="crypto-badge text-xs">
                            {{ ucfirst($product->type) }}
                        </span>
                    </div>
                    
                    <!-- Stock Status -->
                    <div class="absolute top-3 right-3">
                        @if($product->inStock())
                        <div class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                            ✓ In Stock
                        </div>
                        @else
                        <div class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                            ✗ Out of Stock
                        </div>
                        @endif
                    </div>
                    
                    <!-- Quick View Button -->
                    <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <a href="{{ route('shop.product', $product->slug) }}" class="btn-gradient px-6 py-2 text-sm">
                            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                            Quick View
                        </a>
                    </div>
                </div>
                
                <div class="p-6">
                    <div class="flex items-center justify-between mb-3">
                        @if($product->category)
                        <span class="text-xs text-purple-600 dark:text-purple-400 font-medium uppercase tracking-wide">{{ $product->category->name }}</span>
                        @endif
                        <div class="flex items-center text-yellow-400">
                            @for($i = 1; $i <= 5; $i++)
                                <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                    <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/>
                                </svg>
                            @endfor
                        </div>
                    </div>
                    
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300">
                        <a href="{{ route('shop.product', $product->slug) }}">
                            {{ $product->name }}
                        </a>
                    </h3>
                    
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                        {{ $product->short_description }}
                    </p>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex flex-col">
                            <span class="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                                {{ $product->getFormattedPrice() }}
                            </span>
                            @if($product->compare_price && $product->compare_price > $product->price)
                            <span class="text-sm text-gray-500 dark:text-gray-400 line-through">
                                {{ $product->getFormattedComparePrice() }}
                            </span>
                            @endif
                        </div>
                        
                        <button class="btn-crypto px-4 py-2 text-sm">
                            <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"/>
                            </svg>
                            Add to Cart
                        </button>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Call to Action -->
    <div class="relative overflow-hidden">
        <div class="bg-animated py-20">
            <div class="absolute inset-0 bg-black/20"></div>
            <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <div class="float">
                    <div class="inline-flex items-center justify-center w-16 h-16 rounded-full glass mb-6">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                        </svg>
                    </div>
                </div>
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-6 fade-in">
                    Ready to Shop with <span class="bg-gradient-to-r from-yellow-400 to-pink-400 bg-clip-text text-transparent">Crypto</span>?
                </h2>
                <p class="text-xl md:text-2xl text-white/90 mb-10 max-w-3xl mx-auto fade-in">
                    Experience lightning-fast, secure, and decentralized payments on the Polygon network
                </p>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12 fade-in">
                    <a href="{{ route('shop.home') }}" class="btn-gradient inline-flex items-center justify-center px-8 py-4 text-lg">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        Browse All Products
                    </a>
                    <a href="#categories" class="glass px-8 py-4 text-lg font-semibold text-white rounded-lg hover:bg-white/20 transition-all duration-300 inline-flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        Learn More
                    </a>
                </div>
                
                <!-- Features Grid -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 fade-in">
                    <div class="glass p-6 rounded-xl text-center">
                        <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-white mb-2">Secure Payments</h3>
                        <p class="text-white/80 text-sm">Military-grade encryption and blockchain security</p>
                    </div>
                    
                    <div class="glass p-6 rounded-xl text-center">
                        <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-white mb-2">Lightning Fast</h3>
                        <p class="text-white/80 text-sm">Instant transactions on Polygon network</p>
                    </div>
                    
                    <div class="glass p-6 rounded-xl text-center">
                        <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-white mb-2">Low Fees</h3>
                        <p class="text-white/80 text-sm">Minimal transaction costs compared to traditional payment methods</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Floating Elements -->
        <div class="absolute top-10 left-10 w-24 h-24 bg-white/5 rounded-full blur-xl animate-pulse"></div>
        <div class="absolute bottom-10 right-10 w-32 h-32 bg-purple-500/10 rounded-full blur-2xl animate-pulse"></div>
        <div class="absolute top-1/2 left-1/3 w-20 h-20 bg-blue-500/10 rounded-full blur-xl animate-pulse"></div>
    </div>
</div>
@endsection
