@extends('layouts.shop')

@section('title', 'PolyPay Store - Cryptocurrency E-commerce')

@php
    use App\Helpers\CurrencyHelper;
@endphp

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Hero Section -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                    Welcome to PolyPay Store
                </h1>
                <p class="text-xl text-gray-600 dark:text-gray-300 mb-8">
                    Modern e-commerce with cryptocurrency payments on Polygon network
                </p>
                <div class="flex justify-center space-x-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        USDT Payments
                    </span>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                        POL Payments
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories Section -->
    @if($categories->count() > 0)
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-8">Shop by Category</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
            @foreach($categories as $category)
            <a href="{{ route('shop.category', $category->slug) }}" class="group">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6 text-center">
                    @if($category->image)
                    <img src="{{ asset('storage/' . $category->image) }}" alt="{{ $category->name }}" class="w-16 h-16 mx-auto mb-4 rounded-full object-cover">
                    @else
                    <div class="w-16 h-16 mx-auto mb-4 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                        <span class="text-2xl">📦</span>
                    </div>
                    @endif
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400">
                        {{ $category->name }}
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                        {{ $category->activeProducts->count() }} products
                    </p>
                </div>
            </a>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Featured Products Section -->
    @if($featuredProducts->count() > 0)
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-8">Featured Products</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            @foreach($featuredProducts as $product)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
                <a href="{{ route('shop.product', $product->slug) }}">
                    @if($product->primaryImage)
                    <img src="{{ asset('storage/' . $product->primaryImage->image_path) }}" alt="{{ $product->name }}" class="w-full h-48 object-cover">
                    @else
                    <div class="w-full h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                        <span class="text-4xl">📦</span>
                    </div>
                    @endif
                </a>
                <div class="p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $product->type === 'digital' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' }}">
                            {{ ucfirst($product->type) }}
                        </span>
                        @if($product->category)
                        <span class="text-xs text-gray-500 dark:text-gray-400">{{ $product->category->name }}</span>
                        @endif
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        <a href="{{ route('shop.product', $product->slug) }}" class="hover:text-blue-600 dark:hover:text-blue-400">
                            {{ $product->name }}
                        </a>
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                        {{ $product->short_description }}
                    </p>
                    <div class="flex items-center justify-between">
                        <div class="flex flex-col">
                            <span class="text-lg font-bold text-gray-900 dark:text-white">
                                {{ $product->getFormattedPrice() }}
                            </span>
                            @if($product->compare_price && $product->compare_price > $product->price)
                            <span class="text-sm text-gray-500 dark:text-gray-400 line-through">
                                {{ $product->getFormattedComparePrice() }}
                            </span>
                            @endif
                        </div>
                        @if($product->inStock())
                        <span class="text-sm text-green-600 dark:text-green-400 font-medium">In Stock</span>
                        @else
                        <span class="text-sm text-red-600 dark:text-red-400 font-medium">Out of Stock</span>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
    @endif

    <!-- Call to Action -->
    <div class="bg-blue-600 dark:bg-blue-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
            <h2 class="text-3xl font-bold text-white mb-4">
                Ready to Shop with Crypto?
            </h2>
            <p class="text-xl text-blue-100 mb-8">
                Secure, fast, and decentralized payments on Polygon network
            </p>
            <a href="{{ route('shop.home') }}" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors duration-300">
                Browse All Products
            </a>
        </div>
    </div>
</div>
@endsection
