@extends('layouts.shop')

@section('title', 'Order Confirmed - PolyPay Store')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
            <div class="px-6 py-8">
                <!-- Success Header -->
                <div class="text-center mb-8">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 dark:bg-green-900/20 mb-4">
                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                    </div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">Order Confirmed!</h1>
                    <p class="text-gray-600 dark:text-gray-400">
                        Thank you for your purchase. Your payment has been confirmed.
                    </p>
                </div>

                <!-- Order Details -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-8">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Order Details</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Order Number:</span>
                                    <span class="font-medium text-gray-900 dark:text-white">{{ $order->order_number }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Date:</span>
                                    <span class="font-medium text-gray-900 dark:text-white">{{ $order->created_at->format('M j, Y g:i A') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Total:</span>
                                    <span class="font-medium text-gray-900 dark:text-white">${{ number_format($order->total_amount, 2) }}</span>
                                </div>
                            </div>
                        </div>
                        
                        @if($order->payment)
                        <div>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Payment Method:</span>
                                    <span class="font-medium text-gray-900 dark:text-white">{{ $order->payment->crypto_currency }} (Crypto)</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Payment Status:</span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                        Confirmed
                                    </span>
                                </div>
                                @if($order->payment->transaction_hash)
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400">Transaction:</span>
                                    <a href="https://polygonscan.com/tx/{{ $order->payment->transaction_hash }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                                        View on Explorer
                                    </a>
                                </div>
                                @endif
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Order Items -->
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Order Items</h2>
                    
                    <div class="space-y-4">
                        @foreach($order->items as $item)
                        <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                            <div class="flex items-start space-x-4">
                                @if($item->product->primaryImage)
                                    <img src="{{ asset('storage/' . $item->product->primaryImage->image_path) }}" alt="{{ $item->product_name }}" class="w-16 h-16 object-cover rounded">
                                @else
                                    <div class="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center">
                                        <span class="text-2xl">📦</span>
                                    </div>
                                @endif
                                
                                <div class="flex-1">
                                    <h3 class="font-medium text-gray-900 dark:text-white">{{ $item->product_name }}</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">
                                        {{ ucfirst($item->product->type) }} Product
                                    </p>
                                    <div class="flex items-center space-x-4 mt-2">
                                        <span class="text-sm text-gray-600 dark:text-gray-400">Qty: {{ $item->quantity }}</span>
                                        <span class="text-sm font-medium text-gray-900 dark:text-white">${{ number_format($item->total, 2) }}</span>
                                    </div>
                                </div>

                                <!-- Digital Product Delivery -->
                                @if($item->product->isDigital())
                                <div class="text-right">
                                    @if($item->is_delivered)
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 mb-2">
                                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                            </svg>
                                            Delivered
                                        </span>
                                        
                                        @php
                                            $deliveryData = $item->digital_delivery_data;
                                        @endphp
                                        
                                        @if(isset($deliveryData['type']))
                                            <div class="space-y-2">
                                                @if($deliveryData['type'] === 'file' || $deliveryData['type'] === 'image' || $deliveryData['type'] === 'audio')
                                                    <div>
                                                        <a href="{{ $deliveryData['download_url'] }}" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                                            </svg>
                                                            Download
                                                        </a>
                                                        @if(isset($deliveryData['downloads_remaining']))
                                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                            {{ $deliveryData['downloads_remaining'] }} downloads remaining
                                                        </p>
                                                        @endif
                                                    </div>
                                                @elseif($deliveryData['type'] === 'text')
                                                    <div class="bg-gray-100 dark:bg-gray-900 p-3 rounded text-sm">
                                                        <p class="font-medium text-gray-900 dark:text-white mb-1">Content:</p>
                                                        <p class="text-gray-700 dark:text-gray-300">{{ $deliveryData['content'] }}</p>
                                                    </div>
                                                @elseif($deliveryData['type'] === 'account_credentials')
                                                    <div class="bg-gray-100 dark:bg-gray-900 p-3 rounded text-sm">
                                                        <p class="font-medium text-gray-900 dark:text-white mb-2">Account Credentials:</p>
                                                        @foreach($deliveryData['accounts'] as $account)
                                                        <div class="mb-2 p-2 bg-white dark:bg-gray-800 rounded border">
                                                            <p class="text-xs text-gray-600 dark:text-gray-400">Username: <span class="font-mono">{{ $account['username'] }}</span></p>
                                                            <p class="text-xs text-gray-600 dark:text-gray-400">Password: <span class="font-mono">{{ $account['password'] }}</span></p>
                                                        </div>
                                                        @endforeach
                                                    </div>
                                                @endif
                                            </div>
                                        @endif
                                    @else
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                            Processing
                                        </span>
                                    @endif
                                </div>
                                @endif
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>

                <!-- Next Steps -->
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">What's Next?</h3>
                    
                    <div class="space-y-3 text-sm text-blue-800 dark:text-blue-200">
                        @if($order->hasDigitalProducts())
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                            <span>Your digital products have been delivered above. Download links and content are available immediately.</span>
                        </div>
                        @endif
                        
                        @if($order->hasPhysicalProducts())
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                            <span>Your physical products will be processed and shipped within 1-2 business days.</span>
                        </div>
                        @endif
                        
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                            <span>You will receive an email confirmation with your order details and tracking information.</span>
                        </div>
                        
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                            <span>If you have any questions, please contact our support team.</span>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-center space-x-4 mt-8">
                    <a href="{{ route('shop.index') }}" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-300">
                        Continue Shopping
                    </a>
                    @auth
                    <a href="{{ route('dashboard') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-300">
                        View Orders
                    </a>
                    @endauth
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
