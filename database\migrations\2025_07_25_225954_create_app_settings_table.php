<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('type')->default('string'); // string, boolean, integer, json
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(false); // Whether setting can be accessed publicly
            $table->timestamps();

            $table->index('key');
            $table->index(['key', 'is_public']);
        });

        // Insert default settings
        DB::table('app_settings')->insert([
            [
                'key' => 'checkout_requires_login',
                'value' => 'false',
                'type' => 'boolean',
                'description' => 'Whether customers must be logged in to checkout',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'guest_checkout_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Whether guest checkout is allowed',
                'is_public' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'key' => 'default_product_currency',
                'value' => 'USD',
                'type' => 'string',
                'description' => 'Default currency for new products',
                'is_public' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_settings');
    }
};
