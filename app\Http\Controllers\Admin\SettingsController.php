<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AppSetting;

class SettingsController extends Controller
{
    /**
     * Display the settings page
     */
    public function index()
    {
        $settings = [
            'checkout_requires_login' => AppSetting::get('checkout_requires_login', false),
            'guest_checkout_enabled' => AppSetting::get('guest_checkout_enabled', true),
            'default_product_currency' => AppSetting::get('default_product_currency', 'USD'),
        ];

        $supportedCurrencies = [
            'USD' => 'US Dollar ($)',
            'EUR' => 'Euro (€)',
            'GBP' => 'British Pound (£)',
            'CAD' => 'Canadian Dollar (C$)',
            'AUD' => 'Australian Dollar (A$)',
            'JPY' => 'Japanese Yen (¥)',
        ];

        return view('admin.settings.index', compact('settings', 'supportedCurrencies'));
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $request->validate([
            'checkout_requires_login' => 'boolean',
            'guest_checkout_enabled' => 'boolean',
            'default_product_currency' => 'required|string|in:USD,EUR,GBP,CAD,AUD,JPY',
        ]);

        // Update checkout authentication settings
        AppSetting::set(
            'checkout_requires_login',
            $request->boolean('checkout_requires_login'),
            'boolean',
            'Whether customers must be logged in to checkout'
        );

        AppSetting::set(
            'guest_checkout_enabled',
            $request->boolean('guest_checkout_enabled'),
            'boolean',
            'Whether guest checkout is allowed'
        );

        AppSetting::set(
            'default_product_currency',
            $request->default_product_currency,
            'string',
            'Default currency for new products'
        );

        return redirect()->route('admin.settings.index')
            ->with('success', 'Settings updated successfully!');
    }

    /**
     * Get checkout authentication status for dashboard
     */
    public function getCheckoutStatus()
    {
        return [
            'checkout_requires_login' => AppSetting::get('checkout_requires_login', false),
            'guest_checkout_enabled' => AppSetting::get('guest_checkout_enabled', true),
        ];
    }
}
