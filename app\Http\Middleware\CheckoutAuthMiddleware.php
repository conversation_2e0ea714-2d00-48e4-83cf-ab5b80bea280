<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\AppSetting;
use Illuminate\Support\Facades\Auth;

class CheckoutAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if checkout requires login
        $checkoutRequiresLogin = AppSetting::get('checkout_requires_login', false);

        // If checkout requires login and user is not authenticated
        if ($checkoutRequiresLogin && !Auth::check()) {
            // Store the intended URL for redirect after login
            session(['url.intended' => $request->url()]);

            // Redirect to login with a message
            return redirect()->route('login')->with('message', 'Please log in to continue with checkout.');
        }

        // If guest checkout is disabled and user is not authenticated
        $guestCheckoutEnabled = AppSetting::get('guest_checkout_enabled', true);
        if (!$guestCheckoutEnabled && !Auth::check()) {
            session(['url.intended' => $request->url()]);
            return redirect()->route('login')->with('message', 'Guest checkout is disabled. Please log in to continue.');
        }

        return $next($request);
    }
}
