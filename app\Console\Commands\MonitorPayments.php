<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\PaymentService;
use Illuminate\Support\Facades\Log;

class MonitorPayments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payments:monitor 
                            {--once : Run once instead of continuously}
                            {--interval=30 : Interval in seconds between checks (default: 30)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor pending payments and check for blockchain confirmations';

    protected $paymentService;

    /**
     * Create a new command instance.
     */
    public function __construct(PaymentService $paymentService)
    {
        parent::__construct();
        $this->paymentService = $paymentService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting payment monitoring...');
        
        $runOnce = $this->option('once');
        $interval = (int) $this->option('interval');

        if ($runOnce) {
            $this->runMonitoringCycle();
        } else {
            $this->info("Running continuous monitoring with {$interval}s intervals. Press Ctrl+C to stop.");
            
            while (true) {
                $this->runMonitoringCycle();
                sleep($interval);
            }
        }

        return 0;
    }

    /**
     * Run a single monitoring cycle
     */
    private function runMonitoringCycle(): void
    {
        try {
            $this->line('Checking pending payments...');
            
            $results = $this->paymentService->monitorPayments();
            
            $this->info("Monitoring cycle completed:");
            $this->line("  - Checked payments: {$results['checked_payments']}");
            $this->line("  - Confirmed payments: {$results['confirmed_payments']}");
            $this->line("  - Expired payments: {$results['expired_payments']}");
            
            if (!empty($results['errors'])) {
                $this->warn("  - Errors: " . count($results['errors']));
                foreach ($results['errors'] as $error) {
                    $this->error("    " . json_encode($error));
                }
            }
            
            if ($results['confirmed_payments'] > 0) {
                $this->info("✓ {$results['confirmed_payments']} payment(s) confirmed!");
            }
            
            if ($results['expired_payments'] > 0) {
                $this->warn("⚠ {$results['expired_payments']} payment(s) expired");
            }
            
        } catch (\Exception $e) {
            $this->error('Error during monitoring cycle: ' . $e->getMessage());
            Log::error('Payment monitoring command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
