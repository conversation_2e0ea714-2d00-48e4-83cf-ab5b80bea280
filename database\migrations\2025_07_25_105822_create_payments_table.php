<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->string('payment_id')->unique(); // unique payment identifier
            $table->enum('payment_method', ['crypto']);
            $table->enum('crypto_currency', ['USDT', 'POL'])->nullable();
            $table->string('wallet_address'); // generated wallet address for this payment
            $table->decimal('amount', 20, 8); // crypto amount with high precision
            $table->decimal('amount_usd', 10, 2); // USD equivalent at time of payment
            $table->string('transaction_hash')->nullable(); // blockchain transaction hash
            $table->enum('status', ['pending', 'confirmed', 'failed', 'expired'])->default('pending');
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->json('blockchain_data')->nullable(); // additional blockchain info
            $table->text('failure_reason')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
