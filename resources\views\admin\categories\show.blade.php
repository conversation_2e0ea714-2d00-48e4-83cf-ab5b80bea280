@extends('layouts.admin')

@section('title', $category->name . ' - Category Details')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Category Details</h1>
        <div class="flex space-x-3">
            <a href="{{ route('admin.categories.edit', $category) }}" 
               class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                ✏️ Edit Category
            </a>
            <a href="{{ route('admin.categories.index') }}" 
               class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                ← Back to Categories
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Category Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">📂 Category Information</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category Name</label>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $category->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">URL Slug</label>
                        <p class="text-gray-900 dark:text-white font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                            {{ $category->slug }}
                        </p>
                    </div>
                    
                    @if($category->description)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
                        <div class="prose dark:prose-invert max-w-none">
                            <p class="text-gray-900 dark:text-white">{{ $category->description }}</p>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Products in Category -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">📦 Products in Category</h2>
                    <span class="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium">
                        {{ $category->products->count() }} products
                    </span>
                </div>
                
                @if($category->products->count() > 0)
                    <div class="space-y-3">
                        @foreach($category->products as $product)
                            <div class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        <div class="h-12 w-12 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                                            <span class="text-gray-600 dark:text-gray-400 font-medium text-sm">
                                                {{ strtoupper(substr($product->name, 0, 2)) }}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $product->name }}
                                        </h3>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">
                                            {{ $product->getFormattedPrice() }}
                                            @if($product->stock_quantity !== null)
                                                • {{ $product->stock_quantity }} in stock
                                            @endif
                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {{ $product->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                                        {{ $product->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                    <a href="{{ route('admin.products.show', $product) }}" 
                                       class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                                        View
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <div class="text-gray-500 dark:text-gray-400 text-lg mb-4">
                            No products in this category yet.
                        </div>
                        <a href="{{ route('admin.products.create') }}" 
                           class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                            Add First Product
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar Information -->
        <div class="space-y-6">
            <!-- Status & Statistics -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">📊 Status & Statistics</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            {{ $category->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                            {{ $category->is_active ? '✅ Active' : '❌ Inactive' }}
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Total Products</label>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $category->products->count() }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Active Products</label>
                        <p class="text-2xl font-bold text-green-600 dark:text-green-400">
                            {{ $category->products->where('is_active', true)->count() }}
                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Inactive Products</label>
                        <p class="text-2xl font-bold text-red-600 dark:text-red-400">
                            {{ $category->products->where('is_active', false)->count() }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Metadata -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">📅 Metadata</h2>
                
                <div class="space-y-3 text-sm">
                    <div>
                        <label class="block text-gray-700 dark:text-gray-300 font-medium">Created</label>
                        <p class="text-gray-900 dark:text-white">{{ $category->created_at->format('M j, Y \a\t g:i A') }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 dark:text-gray-300 font-medium">Last Updated</label>
                        <p class="text-gray-900 dark:text-white">{{ $category->updated_at->format('M j, Y \a\t g:i A') }}</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">⚡ Quick Actions</h2>
                
                <div class="space-y-3">
                    <a href="{{ route('admin.categories.edit', $category) }}" 
                       class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                        ✏️ Edit Category
                    </a>
                    
                    <a href="{{ route('admin.products.create') }}?category={{ $category->id }}" 
                       class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                        ➕ Add Product to Category
                    </a>
                    
                    <form action="{{ route('admin.categories.toggle-status', $category) }}" method="POST" class="w-full">
                        @csrf
                        <button type="submit" 
                                class="w-full {{ $category->is_active ? 'bg-orange-600 hover:bg-orange-700' : 'bg-green-600 hover:bg-green-700' }} text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                            {{ $category->is_active ? '⏸️ Deactivate' : '▶️ Activate' }}
                        </button>
                    </form>
                    
                    @if($category->products->count() === 0)
                        <form action="{{ route('admin.categories.destroy', $category) }}" method="POST" class="w-full"
                              onsubmit="return confirm('Are you sure you want to delete this category? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" 
                                    class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                                🗑️ Delete Category
                            </button>
                        </form>
                    @else
                        <div class="w-full bg-gray-400 text-white font-medium py-2 px-4 rounded-lg text-center cursor-not-allowed">
                            🗑️ Cannot Delete (Has Products)
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
