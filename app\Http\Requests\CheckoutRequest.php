<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Product;

class CheckoutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1|max:10',
            'crypto_currency' => 'required|in:USDT,POL',

            // Shipping address (required for physical products)
            'shipping_address' => 'required_if:needs_shipping,true|array',
            'shipping_address.address_line_1' => 'required_if:needs_shipping,true|string|max:255',
            'shipping_address.address_line_2' => 'nullable|string|max:255',
            'shipping_address.city' => 'required_if:needs_shipping,true|string|max:100',
            'shipping_address.state' => 'required_if:needs_shipping,true|string|max:100',
            'shipping_address.postal_code' => 'required_if:needs_shipping,true|string|max:20',
            'shipping_address.country' => 'required_if:needs_shipping,true|string|max:100',
        ];
    }

    /**
     * Get custom validation messages
     */
    public function messages(): array
    {
        return [
            'customer_name.required' => 'Customer name is required.',
            'customer_email.required' => 'Email address is required.',
            'customer_email.email' => 'Please provide a valid email address.',
            'product_id.required' => 'Product selection is required.',
            'product_id.exists' => 'Selected product is not available.',
            'quantity.required' => 'Quantity is required.',
            'quantity.min' => 'Quantity must be at least 1.',
            'quantity.max' => 'Maximum quantity allowed is 10.',
            'crypto_currency.required' => 'Payment currency is required.',
            'crypto_currency.in' => 'Invalid payment currency selected.',
            'shipping_address.address_line_1.required_if' => 'Street address is required for physical products.',
            'shipping_address.city.required_if' => 'City is required for physical products.',
            'shipping_address.state.required_if' => 'State/Province is required for physical products.',
            'shipping_address.postal_code.required_if' => 'Postal code is required for physical products.',
            'shipping_address.country.required_if' => 'Country is required for physical products.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Check if product is active and in stock
            if ($this->product_id) {
                $product = Product::find($this->product_id);

                if ($product && !$product->is_active) {
                    $validator->errors()->add('product_id', 'This product is no longer available.');
                }

                if ($product && !$product->inStock($this->quantity)) {
                    $validator->errors()->add('quantity', 'Insufficient stock available.');
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Determine if shipping is needed
        if ($this->product_id) {
            $product = Product::find($this->product_id);
            $this->merge([
                'needs_shipping' => $product && $product->type === 'physical'
            ]);
        }
    }
}
