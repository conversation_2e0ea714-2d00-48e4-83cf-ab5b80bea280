<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->alias([
            'admin' => \App\Http\Middleware\AdminMiddleware::class,
            'currency' => \App\Http\Middleware\CurrencyMiddleware::class,
            'checkout.auth' => \App\Http\Middleware\CheckoutAuthMiddleware::class,
        ]);

        // Apply currency middleware to web routes
        $middleware->web(append: [
            \App\Http\Middleware\CurrencyMiddleware::class,
        ]);
    })
    ->withProviders([
        App\Providers\Web3ServiceProvider::class,
    ])
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
