<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ShopController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\DigitalDownloadController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\ProductController as AdminProductController;
use App\Http\Controllers\Admin\CategoryController as AdminCategoryController;
use Illuminate\Support\Facades\Route;

// Public routes
Route::get('/', [ShopController::class, 'index'])->name('shop.index');
Route::get('/shop', [ShopController::class, 'index'])->name('shop.home');
Route::get('/search', [ShopController::class, 'search'])->name('shop.search');
Route::get('/product/{product:slug}', [ShopController::class, 'show'])->name('shop.product');
Route::get('/category/{category:slug}', [ShopController::class, 'category'])->name('shop.category');

// Checkout routes (with authentication middleware)
Route::middleware(['checkout.auth'])->group(function () {
    Route::get('/checkout', [CheckoutController::class, 'index'])->name('checkout.index');
    Route::post('/checkout', [CheckoutController::class, 'store'])->name('checkout.store');
    Route::get('/checkout/payment/{payment:payment_id}', [CheckoutController::class, 'payment'])->name('checkout.payment');
    Route::get('/checkout/success/{order}', [CheckoutController::class, 'success'])->name('checkout.success');
});

// Digital download routes
Route::get('/digital/download/{token}', [DigitalDownloadController::class, 'download'])->name('digital.download');
Route::get('/digital/view/{token}', [DigitalDownloadController::class, 'show'])->name('digital.view');

// Customer dashboard
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\CustomerDashboardController::class, 'index'])->name('dashboard');
    Route::get('/my-orders', [App\Http\Controllers\CustomerDashboardController::class, 'orders'])->name('customer.orders');
    Route::get('/my-orders/{order}', [App\Http\Controllers\CustomerDashboardController::class, 'orderShow'])->name('customer.orders.show');
    Route::get('/my-downloads', [App\Http\Controllers\CustomerDashboardController::class, 'downloads'])->name('customer.downloads');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // Product management
    Route::resource('products', AdminProductController::class);
    Route::post('products/{product}/toggle-status', [AdminProductController::class, 'toggleStatus'])->name('products.toggle-status');

    // Category management
    Route::resource('categories', AdminCategoryController::class);
    Route::post('categories/{category}/toggle-status', [AdminCategoryController::class, 'toggleStatus'])->name('categories.toggle-status');

    // Order management
    Route::get('orders', [DashboardController::class, 'orders'])->name('orders.index');
    Route::get('orders/{order}', [DashboardController::class, 'orderShow'])->name('orders.show');

    // Payment management
    Route::get('payments', [DashboardController::class, 'payments'])->name('payments.index');

    // Wallet management
    Route::get('wallet', [\App\Http\Controllers\Admin\WalletController::class, 'index'])->name('wallet.index');
    Route::get('wallet/create', [\App\Http\Controllers\Admin\WalletController::class, 'create'])->name('wallet.create');
    Route::post('wallet/generate', [\App\Http\Controllers\Admin\WalletController::class, 'generate'])->name('wallet.generate');
    Route::post('wallet/store', [\App\Http\Controllers\Admin\WalletController::class, 'store'])->name('wallet.store');
    Route::post('wallet/check-balance', [\App\Http\Controllers\Admin\WalletController::class, 'checkBalance'])->name('wallet.check-balance');
    Route::post('wallet/cleanup', [\App\Http\Controllers\Admin\WalletController::class, 'cleanup'])->name('wallet.cleanup');
    Route::get('wallet/{wallet}', [\App\Http\Controllers\Admin\WalletController::class, 'show'])->name('wallet.show');

    // Settings
    Route::get('settings', [\App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('settings.index');
    Route::post('settings', [\App\Http\Controllers\Admin\SettingsController::class, 'update'])->name('settings.update');
});

require __DIR__.'/auth.php';
