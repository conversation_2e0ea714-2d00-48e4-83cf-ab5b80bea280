<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Order;
use App\Models\Payment;
use App\Models\User;
use App\Models\AdminSetting;
use App\Services\PaymentService;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * Show admin dashboard
     */
    public function index()
    {
        // Get dashboard statistics
        $stats = [
            'total_products' => Product::count(),
            'active_products' => Product::where('is_active', true)->count(),
            'total_orders' => Order::count(),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'total_customers' => User::where('role', 'customer')->count(),
            'total_revenue' => Order::whereHas('payment', function($query) {
                $query->where('status', 'confirmed');
            })->sum('total_amount'),
        ];

        // Get recent orders
        $recentOrders = Order::with(['payment', 'items.product'])
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        // Get payment statistics
        $paymentStats = $this->paymentService->getPaymentStats();

        // Get low stock products
        $lowStockProducts = Product::where('track_stock', true)
            ->where('stock_quantity', '<=', 5)
            ->where('is_active', true)
            ->get();

        return view('admin.dashboard', compact('stats', 'recentOrders', 'paymentStats', 'lowStockProducts'));
    }

    /**
     * Show orders list
     */
    public function orders(Request $request)
    {
        $query = Order::with(['payment', 'items.product', 'user']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search by order number or customer email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%");
            });
        }

        $orders = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.orders.index', compact('orders'));
    }

    /**
     * Show order details
     */
    public function orderShow(Order $order)
    {
        $order->load(['payment.wallet', 'items.product', 'user']);

        return view('admin.orders.show', compact('order'));
    }

    /**
     * Show payments list
     */
    public function payments(Request $request)
    {
        $query = Payment::with(['order']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by currency
        if ($request->filled('currency')) {
            $query->where('crypto_currency', $request->currency);
        }

        $payments = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.payments.index', compact('payments'));
    }

    /**
     * Show settings page
     */
    public function settings()
    {
        $settings = AdminSetting::all()->keyBy('key');

        return view('admin.settings', compact('settings'));
    }

    /**
     * Update settings
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'shop_name' => 'required|string|max:255',
            'shop_description' => 'nullable|string',
            'admin_wallet_address' => 'nullable|string|max:255',
            'guest_checkout_enabled' => 'boolean',
            'payment_timeout_minutes' => 'required|integer|min:5|max:120',
        ]);

        $settingsData = [
            'shop_name' => $request->shop_name,
            'shop_description' => $request->shop_description,
            'admin_wallet_address' => $request->admin_wallet_address,
            'guest_checkout_enabled' => $request->boolean('guest_checkout_enabled') ? 'true' : 'false',
            'payment_timeout_minutes' => $request->payment_timeout_minutes,
        ];

        foreach ($settingsData as $key => $value) {
            AdminSetting::updateOrCreate(
                ['key' => $key],
                ['value' => $value]
            );
        }

        return redirect()->route('admin.settings')->with('success', 'Settings updated successfully.');
    }
}
