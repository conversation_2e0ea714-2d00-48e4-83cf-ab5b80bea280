<?php

namespace App\Services;

use Web3\Web3;
use Web3\Contract;
use Web3\Utils;
use Web3\Providers\HttpProvider;
use Web3\RequestManagers\HttpRequestManager;
use Exception;
use Illuminate\Support\Facades\Log;

class Web3Service
{
    protected $web3;
    protected $provider;
    protected $chainId;
    protected $rpcUrl;

    public function __construct()
    {
        $this->rpcUrl = config('web3.use_testnet')
            ? config('web3.polygon_testnet_rpc_url')
            : config('web3.polygon_rpc_url');

        $this->chainId = config('web3.use_testnet')
            ? config('web3.polygon_testnet_chain_id')
            : config('web3.polygon_chain_id');

        // Initialize real Web3 provider
        try {
            $requestManager = new HttpRequestManager($this->rpcUrl);
            $this->provider = new HttpProvider($requestManager);
            $this->web3 = new Web3($this->provider);

            Log::info('Web3Service initialized', [
                'rpc_url' => $this->rpcUrl,
                'chain_id' => $this->chainId,
                'testnet' => config('web3.use_testnet')
            ]);
        } catch (Exception $e) {
            Log::error('Failed to initialize Web3 provider', [
                'error' => $e->getMessage(),
                'rpc_url' => $this->rpcUrl
            ]);

            // Fallback to null for graceful degradation
            $this->provider = null;
            $this->web3 = null;
        }
    }

    /**
     * Generate a new wallet address and private key
     */
    public function generateWallet(): array
    {
        try {
            // Generate a random private key
            $privateKey = bin2hex(random_bytes(32));
            
            // Derive the public key and address from the private key
            $address = $this->privateKeyToAddress($privateKey);
            
            return [
                'address' => $address,
                'private_key' => $privateKey
            ];
        } catch (Exception $e) {
            Log::error('Failed to generate wallet: ' . $e->getMessage());
            throw new Exception('Failed to generate wallet');
        }
    }

    /**
     * Convert private key to Ethereum address
     */
    private function privateKeyToAddress(string $privateKey): string
    {
        // Mock implementation for demo purposes
        // In production, you should use a proper cryptographic library
        return '0x' . bin2hex(random_bytes(20));
    }

    /**
     * Convert private key to public key (mock implementation)
     */
    private function privateKeyToPublicKey(string $privateKey): string
    {
        // Mock implementation for demo purposes
        return '04' . hash('sha256', $privateKey) . hash('sha256', $privateKey . 'public');
    }

    /**
     * Get balance of a token for an address
     */
    public function getTokenBalance(string $address, string $tokenContract): string
    {
        try {
            if (!$this->provider || !$this->web3) {
                Log::warning('Web3 provider not initialized, returning zero balance');
                return '0';
            }

            $contract = new Contract($this->provider, $this->getERC20ABI());
            $contract->at($tokenContract);

            $balance = null;
            $error = null;

            $contract->call('balanceOf', $address, function ($err, $result) use (&$balance, &$error) {
                if ($err !== null) {
                    $error = $err;
                    return;
                }
                if (isset($result[0])) {
                    $balance = $result[0]->toString();
                }
            });

            if ($error !== null) {
                throw new Exception('Error getting token balance: ' . $error->getMessage());
            }

            $balanceStr = $balance ?? '0';

            Log::debug('Retrieved token balance', [
                'address' => $address,
                'token_contract' => $tokenContract,
                'balance' => $balanceStr
            ]);

            return $balanceStr;
        } catch (Exception $e) {
            Log::error('Failed to get token balance', [
                'address' => $address,
                'token_contract' => $tokenContract,
                'error' => $e->getMessage()
            ]);
            return '0';
        }
    }

    /**
     * Get ETH/MATIC balance
     */
    public function getEthBalance(string $address): string
    {
        try {
            if (!$this->provider || !$this->web3) {
                Log::warning('Web3 provider not initialized, returning zero balance');
                return '0';
            }

            $balance = null;
            $error = null;

            $this->web3->eth->getBalance($address, function ($err, $result) use (&$balance, &$error) {
                if ($err !== null) {
                    $error = $err;
                    return;
                }
                $balance = $result->toString();
            });

            if ($error !== null) {
                throw new Exception('Error getting ETH/MATIC balance: ' . $error->getMessage());
            }

            $balanceStr = $balance ?? '0';

            Log::debug('Retrieved ETH/MATIC balance', [
                'address' => $address,
                'balance' => $balanceStr
            ]);

            return $balanceStr;
        } catch (Exception $e) {
            Log::error('Failed to get ETH/MATIC balance', [
                'address' => $address,
                'error' => $e->getMessage()
            ]);
            return '0';
        }
    }

    /**
     * Transfer tokens from one address to another
     */
    public function transferTokens(string $fromPrivateKey, string $toAddress, string $amount, string $tokenContract): array
    {
        try {
            $fromAddress = $this->privateKeyToAddress($fromPrivateKey);
            
            // Get nonce
            $nonce = $this->getNonce($fromAddress);
            
            // Prepare transaction data
            $contract = new Contract($this->provider, $this->getERC20ABI());
            $contract->at($tokenContract);
            
            $data = $contract->getData('transfer', $toAddress, Utils::toHex($amount, true));
            
            $transaction = [
                'from' => $fromAddress,
                'to' => $tokenContract,
                'gas' => Utils::toHex(100000, true),
                'gasPrice' => Utils::toHex(20000000000, true), // 20 Gwei
                'value' => '0x0',
                'data' => $data,
                'nonce' => Utils::toHex($nonce, true),
                'chainId' => $this->chainId
            ];
            
            // Sign and send transaction
            $signedTransaction = $this->signTransaction($transaction, $fromPrivateKey);
            $txHash = $this->sendRawTransaction($signedTransaction);
            
            return [
                'success' => true,
                'transaction_hash' => $txHash,
                'from' => $fromAddress,
                'to' => $toAddress,
                'amount' => $amount
            ];
            
        } catch (Exception $e) {
            Log::error('Failed to transfer tokens: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get transaction nonce for an address
     */
    private function getNonce(string $address): int
    {
        $nonce = 0;
        $this->web3->eth->getTransactionCount($address, 'pending', function ($err, $result) use (&$nonce) {
            if ($err !== null) {
                throw new Exception('Error getting nonce: ' . $err->getMessage());
            }
            $nonce = hexdec($result->toString());
        });
        
        return $nonce;
    }

    /**
     * Sign a transaction with private key
     */
    private function signTransaction(array $transaction, string $privateKey): string
    {
        // This is a placeholder for transaction signing
        // In production, use a proper signing library like kornrunner/ethereum-offline-raw-tx
        throw new Exception('Transaction signing not implemented - use proper signing library');
    }

    /**
     * Send raw transaction to the network
     */
    private function sendRawTransaction(string $signedTransaction): string
    {
        $txHash = null;
        $this->web3->eth->sendRawTransaction($signedTransaction, function ($err, $result) use (&$txHash) {
            if ($err !== null) {
                throw new Exception('Error sending transaction: ' . $err->getMessage());
            }
            $txHash = $result;
        });
        
        return $txHash;
    }

    /**
     * Get ERC20 ABI for token interactions
     */
    private function getERC20ABI(): string
    {
        return '[
            {
                "constant": true,
                "inputs": [{"name": "_owner", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"name": "balance", "type": "uint256"}],
                "type": "function"
            },
            {
                "constant": false,
                "inputs": [
                    {"name": "_to", "type": "address"},
                    {"name": "_value", "type": "uint256"}
                ],
                "name": "transfer",
                "outputs": [{"name": "", "type": "bool"}],
                "type": "function"
            },
            {
                "constant": true,
                "inputs": [],
                "name": "decimals",
                "outputs": [{"name": "", "type": "uint8"}],
                "type": "function"
            }
        ]';
    }

    /**
     * Get current block number
     */
    public function getCurrentBlockNumber(): int
    {
        try {
            if (!$this->provider || !$this->web3) {
                Log::warning('Web3 provider not initialized, returning 0 block number');
                return 0;
            }

            $blockNumber = null;
            $error = null;

            $this->web3->eth->blockNumber(function ($err, $result) use (&$blockNumber, &$error) {
                if ($err !== null) {
                    $error = $err;
                    return;
                }
                $blockNumber = hexdec($result->toString());
            });

            if ($error !== null) {
                throw new Exception('Error getting block number: ' . $error->getMessage());
            }

            return $blockNumber ?? 0;
        } catch (Exception $e) {
            Log::error('Failed to get current block number', [
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * Get transaction receipt with block confirmation info
     */
    public function getTransactionReceipt(string $txHash): ?array
    {
        try {
            if (!$this->provider || !$this->web3) {
                Log::warning('Web3 provider not initialized');
                return null;
            }

            $receipt = null;
            $error = null;

            $this->web3->eth->getTransactionReceipt($txHash, function ($err, $result) use (&$receipt, &$error) {
                if ($err !== null) {
                    $error = $err;
                    return;
                }
                $receipt = $result;
            });

            if ($error !== null) {
                Log::debug('Transaction receipt not found', ['tx_hash' => $txHash]);
                return null;
            }

            if (!$receipt) {
                return null;
            }

            return [
                'status' => $receipt->status ?? null,
                'block_number' => isset($receipt->blockNumber) ? hexdec($receipt->blockNumber) : null,
                'gas_used' => isset($receipt->gasUsed) ? hexdec($receipt->gasUsed) : null,
                'transaction_hash' => $receipt->transactionHash ?? $txHash,
            ];
        } catch (Exception $e) {
            Log::error('Failed to get transaction receipt', [
                'tx_hash' => $txHash,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Check if a transaction is confirmed with required confirmations
     */
    public function isTransactionConfirmed(string $txHash, int $requiredConfirmations = null): array
    {
        try {
            $requiredConfirmations = $requiredConfirmations ?? config('web3.confirmation_blocks', 12);

            $receipt = $this->getTransactionReceipt($txHash);
            if (!$receipt) {
                return [
                    'confirmed' => false,
                    'confirmations' => 0,
                    'required' => $requiredConfirmations
                ];
            }

            // Check if transaction was successful
            if (!isset($receipt['status']) || $receipt['status'] !== '0x1') {
                return [
                    'confirmed' => false,
                    'confirmations' => 0,
                    'required' => $requiredConfirmations,
                    'failed' => true
                ];
            }

            $currentBlock = $this->getCurrentBlockNumber();
            $txBlock = $receipt['block_number'];
            $confirmations = $currentBlock - $txBlock;

            return [
                'confirmed' => $confirmations >= $requiredConfirmations,
                'confirmations' => $confirmations,
                'required' => $requiredConfirmations,
                'tx_block' => $txBlock,
                'current_block' => $currentBlock
            ];
        } catch (Exception $e) {
            Log::error('Failed to check transaction confirmation', [
                'tx_hash' => $txHash,
                'error' => $e->getMessage()
            ]);
            return [
                'confirmed' => false,
                'confirmations' => 0,
                'required' => $requiredConfirmations ?? 12
            ];
        }
    }
}
