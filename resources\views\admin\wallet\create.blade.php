@extends('layouts.admin')

@section('title', 'Admin Wallet Setup')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            {{ $currentAddress ? 'Update Admin Wallet' : 'Set Up Admin Wallet' }}
        </h1>
        <a href="{{ route('admin.wallet.index') }}" 
           class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
            ← Back to Wallet Management
        </a>
    </div>

    @if($errors->any())
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <ul>
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {{ session('error') }}
        </div>
    @endif

    <form action="{{ route('admin.wallet.store') }}" method="POST" class="space-y-6">
        @csrf
        
        <!-- Current Wallet Status -->
        @if($currentAddress)
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
            <h3 class="text-lg font-medium text-blue-900 dark:text-blue-200 mb-3">🔄 Current Admin Wallet</h3>
            <div class="text-sm text-blue-800 dark:text-blue-300">
                <strong>Current Address:</strong> 
                <code class="bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded text-xs">{{ $currentAddress }}</code>
            </div>
            <p class="text-xs text-blue-700 dark:text-blue-400 mt-2">
                You can update the wallet address and private key below. This will affect where future payments are sent.
            </p>
        </div>
        @endif

        <!-- Wallet Configuration -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">🏦 Wallet Configuration</h2>
            
            <div class="space-y-4">
                <!-- Wallet Address -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Wallet Address *
                    </label>
                    <input type="text" name="wallet_address" value="{{ old('wallet_address', $currentAddress) }}" required
                           placeholder="******************************************"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white font-mono text-sm">
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        Enter a valid Ethereum wallet address (must start with 0x and be 42 characters long)
                    </p>
                </div>

                <!-- Private Key -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Private Key *
                    </label>
                    <div class="relative">
                        <input type="password" name="private_key" value="{{ old('private_key') }}" required
                               placeholder="Enter your wallet's private key (64 hex characters)"
                               class="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white font-mono text-sm">
                        <button type="button" onclick="togglePrivateKey()" 
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600">
                            <svg id="eyeIcon" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                        </button>
                    </div>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        Private key must be 64 hexadecimal characters (with or without 0x prefix)
                    </p>
                </div>
            </div>
        </div>

        <!-- Wallet Generation -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">🎲 Generate New Wallet</h2>
            
            <div class="space-y-4">
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    Don't have a wallet? You can generate a new one automatically. 
                    <strong class="text-red-600 dark:text-red-400">Make sure to save the private key securely!</strong>
                </p>
                
                <button type="button" onclick="generateWallet()" 
                        class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                    🎲 Generate New Wallet
                </button>
                
                <div id="generatedWallet" class="hidden mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <h4 class="font-medium text-purple-900 dark:text-purple-200 mb-2">Generated Wallet:</h4>
                    <div class="space-y-2 text-sm">
                        <div>
                            <strong>Address:</strong> 
                            <code id="generatedAddress" class="bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded text-xs"></code>
                        </div>
                        <div>
                            <strong>Private Key:</strong> 
                            <code id="generatedPrivateKey" class="bg-purple-100 dark:bg-purple-800 px-2 py-1 rounded text-xs"></code>
                        </div>
                    </div>
                    <button type="button" onclick="useGeneratedWallet()" 
                            class="mt-3 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium py-1 px-3 rounded transition duration-200">
                        Use This Wallet
                    </button>
                </div>
            </div>
        </div>

        <!-- Supported Currencies -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">💰 Supported Currencies</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                @foreach($supportedCurrencies as $currency)
                    <div class="flex items-center p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                <span class="text-blue-600 dark:text-blue-400 text-xs font-bold">{{ substr($currency, 0, 2) }}</span>
                            </div>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $currency }}</p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                @switch($currency)
                                    @case('USDT') Tether USD @break
                                    @case('POL') Polygon @break
                                    @default {{ $currency }}
                                @endswitch
                            </p>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <p class="text-xs text-gray-600 dark:text-gray-400 mt-4">
                Your admin wallet will receive payments in all supported currencies.
            </p>
        </div>

        <!-- Security Warning -->
        <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-6">
            <h3 class="text-lg font-medium text-red-900 dark:text-red-200 mb-3">🔒 Security Important</h3>
            <ul class="space-y-2 text-sm text-red-800 dark:text-red-300">
                <li>• <strong>Never share your private key</strong> with anyone</li>
                <li>• <strong>Store your private key securely</strong> - if lost, you cannot recover your funds</li>
                <li>• <strong>Use a hardware wallet</strong> for maximum security in production</li>
                <li>• <strong>Test with small amounts</strong> before processing large payments</li>
                <li>• <strong>Keep backups</strong> of your wallet information in multiple secure locations</li>
            </ul>
        </div>

        <!-- Submit Buttons -->
        <div class="flex justify-end space-x-4">
            <a href="{{ route('admin.wallet.index') }}" 
               class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-6 rounded-lg transition duration-200">
                Cancel
            </a>
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition duration-200">
                {{ $currentAddress ? '💾 Update Wallet' : '🏦 Set Up Wallet' }}
            </button>
        </div>
    </form>
</div>

<script>
function togglePrivateKey() {
    const input = document.querySelector('input[name="private_key"]');
    const icon = document.getElementById('eyeIcon');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
        `;
    } else {
        input.type = 'password';
        icon.innerHTML = `
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
        `;
    }
}

function generateWallet() {
    fetch('{{ route("admin.wallet.generate") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('generatedAddress').textContent = data.wallet.address;
            document.getElementById('generatedPrivateKey').textContent = data.wallet.private_key;
            document.getElementById('generatedWallet').classList.remove('hidden');
        } else {
            alert('Failed to generate wallet: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error generating wallet. Please try again.');
        console.error('Error:', error);
    });
}

function useGeneratedWallet() {
    const address = document.getElementById('generatedAddress').textContent;
    const privateKey = document.getElementById('generatedPrivateKey').textContent;
    
    document.querySelector('input[name="wallet_address"]').value = address;
    document.querySelector('input[name="private_key"]').value = privateKey;
    
    // Hide the generated wallet section
    document.getElementById('generatedWallet').classList.add('hidden');
    
    alert('Generated wallet has been filled into the form. Please review and save.');
}
</script>
@endsection
