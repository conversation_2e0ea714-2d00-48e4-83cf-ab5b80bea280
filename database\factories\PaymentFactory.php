<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Payment>
 */
class PaymentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'payment_id' => strtolower($this->faker->bothify('????????????????????????????????')),
            'payment_method' => 'crypto',
            'amount' => $this->faker->randomFloat(8, 0.1, 1000),
            'amount_usd' => $this->faker->randomFloat(2, 10, 1000),
            'crypto_currency' => $this->faker->randomElement(['USDT', 'POL']),
            'wallet_address' => '0x' . $this->faker->bothify('????????????????????????????????????????'),
            'status' => $this->faker->randomElement(['pending', 'confirmed', 'failed', 'expired']),
            'expires_at' => $this->faker->dateTimeBetween('now', '+2 hours'),
            'transaction_hash' => $this->faker->optional()->bothify('0x????????????????????????????????????????????????????????????????'),
            'confirmed_at' => $this->faker->optional()->dateTimeBetween('-1 hour', 'now'),
        ];
    }
}
