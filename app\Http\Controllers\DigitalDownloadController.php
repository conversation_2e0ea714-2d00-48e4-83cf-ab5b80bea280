<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\DigitalDeliveryService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;

class DigitalDownloadController extends Controller
{
    protected $digitalDeliveryService;

    public function __construct(DigitalDeliveryService $digitalDeliveryService)
    {
        $this->digitalDeliveryService = $digitalDeliveryService;
    }

    /**
     * Handle digital product download
     */
    public function download(string $token)
    {
        try {
            $downloadData = $this->digitalDeliveryService->processDownload($token);

            $filePath = $downloadData['file_path'];
            $fileName = $downloadData['file_name'];
            $mimeType = $downloadData['mime_type'];

            // Get file contents
            $fileContents = Storage::get($filePath);

            return Response::make($fileContents, 200, [
                'Content-Type' => $mimeType,
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Content-Length' => strlen($fileContents),
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0',
            ]);

        } catch (\Exception $e) {
            abort(404, $e->getMessage());
        }
    }

    /**
     * Show download page with product information
     */
    public function show(string $token)
    {
        $validation = $this->digitalDeliveryService->validateDownloadToken($token);

        if (!$validation) {
            abort(404, 'Invalid or expired download link');
        }

        $orderItem = $validation['order_item'];
        $deliveryData = $orderItem->digital_delivery_data;

        return view('digital.download', compact('orderItem', 'deliveryData', 'token'));
    }
}
