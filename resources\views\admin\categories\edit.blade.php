@extends('layouts.admin')

@section('title', 'Edit Category - ' . $category->name)

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Edit Category</h1>
        <div class="flex space-x-3">
            <a href="{{ route('admin.categories.show', $category) }}" 
               class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                👁️ View Category
            </a>
            <a href="{{ route('admin.categories.index') }}" 
               class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                ← Back to Categories
            </a>
        </div>
    </div>

    @if($errors->any())
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <ul>
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    @if(session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {{ session('error') }}
        </div>
    @endif

    <form action="{{ route('admin.categories.update', $category) }}" method="POST" class="space-y-6">
        @csrf
        @method('PUT')
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">📂 Category Information</h2>
            
            <div class="space-y-4">
                <!-- Category Name -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Category Name *
                    </label>
                    <input type="text" name="name" value="{{ old('name', $category->name) }}" required
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                           placeholder="Enter category name">
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        The category name will be used for navigation and product organization.
                    </p>
                </div>

                <!-- Current Slug Display -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Current URL Slug
                    </label>
                    <div class="px-3 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
                        <code class="text-sm text-gray-600 dark:text-gray-400">{{ $category->slug }}</code>
                    </div>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        The URL slug will be automatically updated based on the category name.
                    </p>
                </div>

                <!-- Description -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Description
                    </label>
                    <textarea name="description" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                              placeholder="Enter category description (optional)">{{ old('description', $category->description) }}</textarea>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        Optional description to help customers understand what products are in this category.
                    </p>
                </div>

                <!-- Status -->
                <div class="flex items-center">
                    <input type="checkbox" name="is_active" value="1" {{ old('is_active', $category->is_active) ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                        Category is Active
                    </label>
                </div>
                <p class="text-xs text-gray-600 dark:text-gray-400">
                    Active categories will be visible to customers and available for product assignment.
                </p>
            </div>
        </div>

        <!-- Category Statistics -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">📊 Category Statistics</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {{ $category->products()->count() }}
                    </div>
                    <div class="text-sm text-blue-800 dark:text-blue-300">Total Products</div>
                </div>
                
                <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        {{ $category->products()->where('is_active', true)->count() }}
                    </div>
                    <div class="text-sm text-green-800 dark:text-green-300">Active Products</div>
                </div>
                
                <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {{ $category->created_at->format('M j, Y') }}
                    </div>
                    <div class="text-sm text-purple-800 dark:text-purple-300">Created Date</div>
                </div>
            </div>
        </div>

        <!-- Warning for Categories with Products -->
        @if($category->products()->count() > 0)
        <div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-6">
            <h3 class="text-lg font-medium text-yellow-900 dark:text-yellow-200 mb-3">⚠️ Important Notice</h3>
            <p class="text-sm text-yellow-800 dark:text-yellow-300">
                This category currently has <strong>{{ $category->products()->count() }} products</strong> assigned to it. 
                Changing the category name or deactivating it may affect how these products are displayed to customers.
            </p>
        </div>
        @endif

        <!-- Submit Buttons -->
        <div class="flex justify-end space-x-4">
            <a href="{{ route('admin.categories.show', $category) }}" 
               class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-6 rounded-lg transition duration-200">
                Cancel
            </a>
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition duration-200">
                💾 Update Category
            </button>
        </div>
    </form>
</div>
@endsection
