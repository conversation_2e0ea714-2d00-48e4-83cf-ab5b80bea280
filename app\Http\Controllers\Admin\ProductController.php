<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Category;
use App\Models\DigitalProduct;
use App\Models\AppSetting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Product::with(['category', 'digitalProduct']);

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $products = $query->orderBy('created_at', 'desc')->paginate(20);
        $categories = Category::active()->get();

        return view('admin.products.index', compact('products', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::active()->get();
        $supportedCurrencies = [
            'USD' => 'US Dollar ($)',
            'EUR' => 'Euro (€)',
            'GBP' => 'British Pound (£)',
            'CAD' => 'Canadian Dollar (C$)',
            'AUD' => 'Australian Dollar (A$)',
            'JPY' => 'Japanese Yen (¥)',
        ];
        $defaultCurrency = AppSetting::get('default_product_currency', 'USD');

        return view('admin.products.create', compact('categories', 'supportedCurrencies', 'defaultCurrency'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'type' => 'required|in:physical,digital',
            'price' => 'required|numeric|min:0',
            'compare_price' => 'nullable|numeric|min:0',
            'base_currency' => 'required|string|in:USD,EUR,GBP,CAD,AUD,JPY',
            'category_id' => 'nullable|exists:categories,id',
            'stock_quantity' => 'required_if:track_stock,1|integer|min:0',
            'track_stock' => 'boolean',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|array',

            // Digital product fields
            'digital_type' => 'required_if:type,digital|in:file,text,image,audio,account_credentials,other',
            'file_path' => 'nullable|string',
            'content' => 'nullable|string',
            'is_unlimited' => 'boolean',
            'total_quantity' => 'nullable|integer|min:0',
            'download_limit' => 'nullable|integer|min:1',
            'download_expiry_days' => 'nullable|integer|min:1',
        ]);

        try {
            DB::beginTransaction();

            $product = Product::create([
                'name' => $request->name,
                'description' => $request->description,
                'short_description' => $request->short_description,
                'type' => $request->type,
                'price' => $request->price,
                'compare_price' => $request->compare_price,
                'base_currency' => $request->base_currency,
                'category_id' => $request->category_id,
                'stock_quantity' => $request->track_stock ? $request->stock_quantity : 0,
                'track_stock' => $request->boolean('track_stock'),
                'is_active' => $request->boolean('is_active', true),
                'is_featured' => $request->boolean('is_featured'),
                'weight' => $request->weight,
                'dimensions' => $request->dimensions,
            ]);

            // Create digital product data if needed
            if ($request->type === 'digital') {
                DigitalProduct::create([
                    'product_id' => $product->id,
                    'digital_type' => $request->digital_type,
                    'file_path' => $request->file_path,
                    'content' => $request->content,
                    'is_unlimited' => $request->boolean('is_unlimited'),
                    'total_quantity' => $request->total_quantity ?? 0,
                    'available_quantity' => $request->total_quantity ?? 0,
                    'download_limit' => $request->download_limit,
                    'download_expiry_days' => $request->download_expiry_days,
                ]);
            }

            DB::commit();

            return redirect()->route('admin.products.index')
                ->with('success', 'Product created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error', 'Failed to create product: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        $product->load(['category', 'digitalProduct', 'images', 'orderItems.order']);
        return view('admin.products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        $product->load(['digitalProduct']);
        $categories = Category::active()->get();
        $supportedCurrencies = [
            'USD' => 'US Dollar ($)',
            'EUR' => 'Euro (€)',
            'GBP' => 'British Pound (£)',
            'CAD' => 'Canadian Dollar (C$)',
            'AUD' => 'Australian Dollar (A$)',
            'JPY' => 'Japanese Yen (¥)',
        ];

        return view('admin.products.edit', compact('product', 'categories', 'supportedCurrencies'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'price' => 'required|numeric|min:0',
            'compare_price' => 'nullable|numeric|min:0',
            'base_currency' => 'required|string|in:USD,EUR,GBP,CAD,AUD,JPY',
            'category_id' => 'nullable|exists:categories,id',
            'stock_quantity' => 'required_if:track_stock,1|integer|min:0',
            'track_stock' => 'boolean',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|array',
        ]);

        try {
            $product->update([
                'name' => $request->name,
                'description' => $request->description,
                'short_description' => $request->short_description,
                'price' => $request->price,
                'compare_price' => $request->compare_price,
                'base_currency' => $request->base_currency,
                'category_id' => $request->category_id,
                'stock_quantity' => $request->track_stock ? $request->stock_quantity : 0,
                'track_stock' => $request->boolean('track_stock'),
                'is_active' => $request->boolean('is_active'),
                'is_featured' => $request->boolean('is_featured'),
                'weight' => $request->weight,
                'dimensions' => $request->dimensions,
            ]);

            return redirect()->route('admin.products.index')
                ->with('success', 'Product updated successfully.');

        } catch (\Exception $e) {
            return back()->withInput()
                ->with('error', 'Failed to update product: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        try {
            $product->delete();
            return redirect()->route('admin.products.index')
                ->with('success', 'Product deleted successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete product: ' . $e->getMessage());
        }
    }

    /**
     * Toggle product status
     */
    public function toggleStatus(Product $product)
    {
        $product->update(['is_active' => !$product->is_active]);

        $status = $product->is_active ? 'activated' : 'deactivated';
        return back()->with('success', "Product {$status} successfully.");
    }
}
