@php
    use App\Helpers\CurrencyHelper;
    $currencies = CurrencyHelper::getSupportedCurrencies();
    $selectedCurrency = session('selected_currency', 'USD');
@endphp

<div class="relative inline-block text-left">
    <div>
        <button type="button" 
                class="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:bg-gray-800 dark:text-white dark:ring-gray-600 dark:hover:bg-gray-700" 
                id="currency-menu-button" 
                aria-expanded="true" 
                aria-haspopup="true"
                onclick="toggleCurrencyDropdown()">
            <span class="flex items-center">
                <span class="font-medium">{{ $currencies[$selectedCurrency]['symbol'] ?? $selectedCurrency }}</span>
                <span class="ml-1 text-gray-500 dark:text-gray-400">{{ $selectedCurrency }}</span>
            </span>
            <svg class="-mr-1 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
            </svg>
        </button>
    </div>

    <div class="absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-600 hidden" 
         role="menu" 
         aria-orientation="vertical" 
         aria-labelledby="currency-menu-button" 
         tabindex="-1"
         id="currency-dropdown">
        <div class="py-1" role="none">
            @foreach($currencies as $code => $currency)
                <a href="#" 
                   class="currency-option flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700 {{ $selectedCurrency === $code ? 'bg-gray-100 dark:bg-gray-700' : '' }}" 
                   role="menuitem" 
                   tabindex="-1"
                   data-currency="{{ $code }}"
                   onclick="selectCurrency('{{ $code }}')">
                    <span class="font-medium mr-2">{{ $currency['symbol'] }}</span>
                    <span class="flex-1">{{ $currency['name'] }}</span>
                    <span class="text-gray-500 dark:text-gray-400 text-xs">{{ $code }}</span>
                </a>
            @endforeach
        </div>
    </div>
</div>

<script>
function toggleCurrencyDropdown() {
    const dropdown = document.getElementById('currency-dropdown');
    dropdown.classList.toggle('hidden');
}

function selectCurrency(currency) {
    // Close dropdown
    document.getElementById('currency-dropdown').classList.add('hidden');
    
    // Show loading state
    const button = document.getElementById('currency-menu-button');
    const originalContent = button.innerHTML;
    button.innerHTML = '<span class="flex items-center"><span class="animate-spin mr-2">⟳</span>Changing...</span>';
    
    // Submit currency change
    const form = document.createElement('form');
    form.method = 'GET';
    form.action = window.location.pathname;
    
    // Preserve existing query parameters
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('currency', currency);
    
    // Add all parameters as hidden inputs
    for (const [key, value] of urlParams.entries()) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        form.appendChild(input);
    }
    
    document.body.appendChild(form);
    form.submit();
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('currency-dropdown');
    const button = document.getElementById('currency-menu-button');
    
    if (!dropdown.contains(event.target) && !button.contains(event.target)) {
        dropdown.classList.add('hidden');
    }
});
</script>
