<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\Product;
use App\Models\Category;
use App\Models\User;

class CheckoutTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->category = Category::factory()->create();
        $this->product = Product::factory()->create([
            'category_id' => $this->category->id,
            'price' => 99.99,
            'is_active' => true,
            'stock_quantity' => 10,
            'track_stock' => true,
        ]);
    }

    public function test_checkout_page_displays_correctly()
    {
        $response = $this->get(route('checkout.index', ['product_id' => $this->product->id]));

        $response->assertStatus(200);
        $response->assertSee($this->product->name);
        $response->assertSee('$99.99');
    }

    public function test_checkout_requires_valid_data()
    {
        $response = $this->post(route('checkout.store'), []);

        $response->assertSessionHasErrors([
            'customer_name',
            'customer_email',
            'product_id',
            'quantity',
            'crypto_currency'
        ]);
    }

    public function test_successful_checkout_creates_order()
    {
        $checkoutData = [
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+1234567890',
            'product_id' => $this->product->id,
            'quantity' => 2,
            'crypto_currency' => 'USDT',
        ];

        $response = $this->post(route('checkout.store'), $checkoutData);

        $this->assertDatabaseHas('orders', [
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'status' => 'pending',
        ]);

        $this->assertDatabaseHas('order_items', [
            'product_id' => $this->product->id,
            'quantity' => 2,
            'price' => 99.99,
        ]);

        $this->assertDatabaseHas('payments', [
            'crypto_currency' => 'USDT',
            'status' => 'pending',
        ]);
    }

    public function test_checkout_with_insufficient_stock_fails()
    {
        $this->product->update(['stock_quantity' => 1]);

        $checkoutData = [
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'product_id' => $this->product->id,
            'quantity' => 5, // More than available stock
            'crypto_currency' => 'USDT',
        ];

        $response = $this->post(route('checkout.store'), $checkoutData);

        $response->assertSessionHasErrors(['quantity']);
    }

    public function test_checkout_with_inactive_product_fails()
    {
        $this->product->update(['is_active' => false]);

        $checkoutData = [
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'product_id' => $this->product->id,
            'quantity' => 1,
            'crypto_currency' => 'USDT',
        ];

        $response = $this->post(route('checkout.store'), $checkoutData);

        $response->assertSessionHasErrors(['product_id']);
    }

    public function test_physical_product_requires_shipping_address()
    {
        $physicalProduct = Product::factory()->create([
            'type' => 'physical',
            'category_id' => $this->category->id,
        ]);

        $checkoutData = [
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'product_id' => $physicalProduct->id,
            'quantity' => 1,
            'crypto_currency' => 'USDT',
            // Missing shipping address
        ];

        $response = $this->post(route('checkout.store'), $checkoutData);

        $response->assertSessionHasErrors([
            'shipping_address.address_line_1',
            'shipping_address.city',
            'shipping_address.state',
            'shipping_address.postal_code',
            'shipping_address.country',
        ]);
    }

    public function test_digital_product_does_not_require_shipping()
    {
        $digitalProduct = Product::factory()->create([
            'type' => 'digital',
            'category_id' => $this->category->id,
        ]);

        $checkoutData = [
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'product_id' => $digitalProduct->id,
            'quantity' => 1,
            'crypto_currency' => 'USDT',
        ];

        $response = $this->post(route('checkout.store'), $checkoutData);

        // Should not have shipping address errors
        $response->assertSessionDoesntHaveErrors([
            'shipping_address.address_line_1',
            'shipping_address.city',
        ]);
    }
}
