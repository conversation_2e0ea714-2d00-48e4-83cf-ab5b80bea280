<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\Web3Service;
use Illuminate\Support\Facades\Log;

class TestWeb3 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'web3:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Web3 blockchain integration';

    protected $web3Service;

    /**
     * Create a new command instance.
     */
    public function __construct(Web3Service $web3Service)
    {
        parent::__construct();
        $this->web3Service = $web3Service;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔗 Testing Web3 Blockchain Integration...');
        $this->newLine();

        try {
            // Test 1: Web3 Service instantiation
            $this->line('✓ Web3Service instantiated successfully');
            
            // Test 2: Get current block number
            $this->line('📊 Getting current block number...');
            $blockNumber = $this->web3Service->getCurrentBlockNumber();
            
            if ($blockNumber > 0) {
                $this->info("✓ Current block number: " . number_format($blockNumber));
            } else {
                $this->warn("⚠ Block number returned 0 - may indicate connection issues");
            }
            
            // Test 3: Test balance checking
            $this->line('💰 Testing balance checking...');
            $testAddress = '0x0000000000000000000000000000000000000000'; // Zero address
            
            // Get contract addresses
            $network = config('web3.use_testnet') ? 'testnet' : 'mainnet';
            $usdtContract = config("web3.contracts.{$network}.usdt");
            
            $this->line("Network: " . ($network === 'testnet' ? 'Polygon Mumbai Testnet' : 'Polygon Mainnet'));
            $this->line("USDT Contract: {$usdtContract}");
            
            // Test USDT balance
            $this->line('Checking USDT balance for zero address...');
            $usdtBalance = $this->web3Service->getTokenBalance($testAddress, $usdtContract);
            $this->info("✓ USDT Balance: {$usdtBalance}");
            
            // Test MATIC balance
            $this->line('Checking MATIC balance for zero address...');
            $maticBalance = $this->web3Service->getEthBalance($testAddress);
            $this->info("✓ MATIC Balance: {$maticBalance}");
            
            $this->newLine();
            $this->info('🎉 All Web3 integration tests completed successfully!');
            $this->info('The system is now using real blockchain connections instead of mocks.');
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('❌ Error during Web3 testing: ' . $e->getMessage());
            $this->line('Stack trace:');
            $this->line($e->getTraceAsString());
            
            return 1;
        }
    }
}
