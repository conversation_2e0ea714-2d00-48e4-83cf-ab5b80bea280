@extends('layouts.shop')

@section('title', $product->name . ' - PolyPay Store')

@php
    use App\Helpers\CurrencyHelper;
@endphp

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 p-8">
                <!-- Product Image -->
                <div class="space-y-4">
                    <div class="aspect-square bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                        @if($product->primaryImage)
                            <img src="{{ asset('storage/' . $product->primaryImage->image_path) }}" alt="{{ $product->name }}" class="w-full h-full object-cover rounded-lg">
                        @else
                            <span class="text-6xl">📦</span>
                        @endif
                    </div>
                    
                    @if($product->images->count() > 1)
                    <div class="grid grid-cols-4 gap-2">
                        @foreach($product->images->take(4) as $image)
                        <div class="aspect-square bg-gray-200 dark:bg-gray-700 rounded">
                            <img src="{{ asset('storage/' . $image->image_path) }}" alt="{{ $product->name }}" class="w-full h-full object-cover rounded">
                        </div>
                        @endforeach
                    </div>
                    @endif
                </div>

                <!-- Product Details -->
                <div class="space-y-6">
                    <div>
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $product->type === 'digital' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' }}">
                                {{ ucfirst($product->type) }}
                            </span>
                            @if($product->category)
                            <span class="text-sm text-gray-500 dark:text-gray-400">{{ $product->category->name }}</span>
                            @endif
                        </div>
                        
                        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">{{ $product->name }}</h1>
                        
                        <div class="flex items-center space-x-4 mb-4">
                            <span class="text-3xl font-bold text-gray-900 dark:text-white">
                                {{ $product->getFormattedPrice() }}
                            </span>
                            @if($product->compare_price && $product->compare_price > $product->price)
                            <span class="text-xl text-gray-500 dark:text-gray-400 line-through">
                                {{ $product->getFormattedComparePrice() }}
                            </span>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                Save {{ $product->getFormattedSavings() }}
                            </span>
                            @endif
                        </div>

                        <div class="flex items-center space-x-4 mb-6">
                            @if($product->inStock())
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                    </svg>
                                    In Stock
                                </span>
                                @if($product->track_stock)
                                <span class="text-sm text-gray-600 dark:text-gray-400">{{ $product->stock_quantity }} available</span>
                                @endif
                            @else
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                    </svg>
                                    Out of Stock
                                </span>
                            @endif
                        </div>
                    </div>

                    <!-- Product Description -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Description</h3>
                        <p class="text-gray-600 dark:text-gray-400 leading-relaxed">{{ $product->description }}</p>
                    </div>

                    <!-- Digital Product Info -->
                    @if($product->isDigital() && $product->digitalProduct)
                    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                        <h4 class="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-2">Digital Product</h4>
                        <ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                            <li>• Instant delivery after payment confirmation</li>
                            @if($product->digitalProduct->download_limit)
                            <li>• {{ $product->digitalProduct->download_limit }} downloads allowed</li>
                            @endif
                            @if($product->digitalProduct->download_expiry_days)
                            <li>• Access expires in {{ $product->digitalProduct->download_expiry_days }} days</li>
                            @endif
                        </ul>
                    </div>
                    @endif

                    <!-- Physical Product Info -->
                    @if($product->isPhysical())
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-2">Shipping Information</h4>
                        <ul class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                            <li>• Standard shipping: $10.00</li>
                            <li>• Estimated delivery: 3-7 business days</li>
                            @if($product->weight)
                            <li>• Weight: {{ $product->weight }} kg</li>
                            @endif
                        </ul>
                    </div>
                    @endif

                    <!-- Purchase Form -->
                    <form action="{{ route('checkout.index') }}" method="GET" class="space-y-4">
                        <input type="hidden" name="product_id" value="{{ $product->id }}">
                        
                        <div>
                            <label for="quantity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Quantity
                            </label>
                            <select name="quantity" id="quantity" class="w-20 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                                @for($i = 1; $i <= min(10, $product->track_stock ? $product->stock_quantity : 10); $i++)
                                <option value="{{ $i }}">{{ $i }}</option>
                                @endfor
                            </select>
                        </div>

                        <div class="flex space-x-4">
                            @if($product->inStock())
                            <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-300 flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"/>
                                </svg>
                                Buy Now with Crypto
                            </button>
                            @else
                            <button disabled class="flex-1 bg-gray-400 text-white font-medium py-3 px-6 rounded-lg cursor-not-allowed">
                                Out of Stock
                            </button>
                            @endif
                            
                            <button type="button" class="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-300">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                </svg>
                            </button>
                        </div>
                    </form>

                    <!-- Payment Methods -->
                    <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
                        <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">Accepted Payments</h4>
                        <div class="flex space-x-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                USDT
                            </span>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                POL
                            </span>
                            <span class="text-sm text-gray-500 dark:text-gray-400">on Polygon Network</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Products -->
        @if($relatedProducts->count() > 0)
        <div class="mt-12">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-8">Related Products</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($relatedProducts as $relatedProduct)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden">
                    <a href="{{ route('shop.product', $relatedProduct->slug) }}">
                        @if($relatedProduct->primaryImage)
                        <img src="{{ asset('storage/' . $relatedProduct->primaryImage->image_path) }}" alt="{{ $relatedProduct->name }}" class="w-full h-48 object-cover">
                        @else
                        <div class="w-full h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                            <span class="text-4xl">📦</span>
                        </div>
                        @endif
                    </a>
                    <div class="p-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                            <a href="{{ route('shop.product', $relatedProduct->slug) }}" class="hover:text-blue-600 dark:hover:text-blue-400">
                                {{ $relatedProduct->name }}
                            </a>
                        </h3>
                        <p class="text-lg font-bold text-gray-900 dark:text-white">
                            {{ $relatedProduct->getFormattedPrice() }}
                        </p>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
