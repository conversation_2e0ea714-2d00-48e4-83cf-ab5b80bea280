<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\Web3Service;
use App\Services\WalletService;
use App\Services\PaymentService;
use App\Services\DigitalDeliveryService;
use App\Services\CurrencyService;

class Web3ServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(Web3Service::class, function ($app) {
            return new Web3Service();
        });

        $this->app->singleton(WalletService::class, function ($app) {
            return new WalletService($app->make(Web3Service::class));
        });

        $this->app->singleton(CurrencyService::class, function ($app) {
            return new CurrencyService();
        });

        $this->app->singleton(DigitalDeliveryService::class, function ($app) {
            return new DigitalDeliveryService();
        });

        $this->app->singleton(PaymentService::class, function ($app) {
            return new PaymentService(
                $app->make(WalletService::class),
                $app->make(DigitalDeliveryService::class),
                $app->make(CurrencyService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
