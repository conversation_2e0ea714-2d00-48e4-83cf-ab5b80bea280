@extends('layouts.shop')

@section('title', $category->name . ' - PolyPay Store')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Category Header -->
        <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg p-8 mb-8">
            <div class="text-center">
                @if($category->image)
                <img src="{{ asset('storage/' . $category->image) }}" alt="{{ $category->name }}" class="w-24 h-24 mx-auto mb-4 rounded-full object-cover">
                @else
                <div class="w-24 h-24 mx-auto mb-4 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                    <span class="text-4xl">📦</span>
                </div>
                @endif
                <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">{{ $category->name }}</h1>
                @if($category->description)
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">{{ $category->description }}</p>
                @endif
                <div class="mt-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {{ $products->total() }} {{ Str::plural('product', $products->total()) }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Filters and Sorting -->
        <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg p-6 mb-8">
            <form method="GET" action="{{ route('shop.category', $category->slug) }}" class="flex flex-wrap items-center gap-4">
                <!-- Sort -->
                <div class="flex items-center space-x-2">
                    <label for="sort" class="text-sm font-medium text-gray-700 dark:text-gray-300">Sort by:</label>
                    <select name="sort" id="sort" onchange="this.form.submit()" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="newest" {{ request('sort') === 'newest' ? 'selected' : '' }}>Newest</option>
                        <option value="price_low" {{ request('sort') === 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                        <option value="price_high" {{ request('sort') === 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                        <option value="name" {{ request('sort') === 'name' ? 'selected' : '' }}>Name A-Z</option>
                    </select>
                </div>

                <!-- Product Type Filter -->
                <div class="flex items-center space-x-2">
                    <label for="type" class="text-sm font-medium text-gray-700 dark:text-gray-300">Type:</label>
                    <select name="type" id="type" onchange="this.form.submit()" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="">All Types</option>
                        <option value="physical" {{ request('type') === 'physical' ? 'selected' : '' }}>Physical</option>
                        <option value="digital" {{ request('type') === 'digital' ? 'selected' : '' }}>Digital</option>
                    </select>
                </div>

                <!-- Price Range -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Price:</label>
                    <input type="number" name="min_price" placeholder="Min" value="{{ request('min_price') }}" class="w-20 px-2 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                    <span class="text-gray-500">-</span>
                    <input type="number" name="max_price" placeholder="Max" value="{{ request('max_price') }}" class="w-20 px-2 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                    <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-300">
                        Filter
                    </button>
                </div>

                @if(request()->hasAny(['sort', 'type', 'min_price', 'max_price']))
                <a href="{{ route('shop.category', $category->slug) }}" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-md transition-colors duration-300">
                    Clear Filters
                </a>
                @endif
            </form>
        </div>

        <!-- Products Grid -->
        @if($products->count() > 0)
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
            @foreach($products as $product)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden group">
                <a href="{{ route('shop.product', $product->slug) }}" class="block">
                    <div class="relative">
                        @if($product->primaryImage)
                        <img src="{{ asset('storage/' . $product->primaryImage->image_path) }}" alt="{{ $product->name }}" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                        @else
                        <div class="w-full h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center group-hover:bg-gray-300 dark:group-hover:bg-gray-600 transition-colors duration-300">
                            <span class="text-4xl">📦</span>
                        </div>
                        @endif
                        
                        <!-- Product Type Badge -->
                        <div class="absolute top-2 left-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $product->type === 'digital' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' }}">
                                {{ ucfirst($product->type) }}
                            </span>
                        </div>

                        <!-- Sale Badge -->
                        @if($product->compare_price && $product->compare_price > $product->price)
                        <div class="absolute top-2 right-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Save ${{ number_format($product->compare_price - $product->price, 0) }}
                            </span>
                        </div>
                        @endif

                        <!-- Quick View Overlay -->
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                            <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <span class="bg-white text-gray-900 px-4 py-2 rounded-lg font-medium">Quick View</span>
                            </div>
                        </div>
                    </div>
                </a>
                
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                        <a href="{{ route('shop.product', $product->slug) }}" class="hover:text-blue-600 dark:hover:text-blue-400">
                            {{ $product->name }}
                        </a>
                    </h3>
                    
                    @if($product->short_description)
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                        {{ $product->short_description }}
                    </p>
                    @endif

                    <div class="flex items-center justify-between mb-3">
                        <div class="flex flex-col">
                            <span class="text-lg font-bold text-gray-900 dark:text-white">
                                ${{ number_format($product->price, 2) }}
                            </span>
                            @if($product->compare_price && $product->compare_price > $product->price)
                            <span class="text-sm text-gray-500 dark:text-gray-400 line-through">
                                ${{ number_format($product->compare_price, 2) }}
                            </span>
                            @endif
                        </div>
                        
                        <div class="text-right">
                            @if($product->inStock())
                            <span class="text-sm text-green-600 dark:text-green-400 font-medium">In Stock</span>
                            @if($product->track_stock && $product->stock_quantity <= 5)
                            <div class="text-xs text-orange-600">Only {{ $product->stock_quantity }} left</div>
                            @endif
                            @else
                            <span class="text-sm text-red-600 dark:text-red-400 font-medium">Out of Stock</span>
                            @endif
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        <a href="{{ route('checkout.index', ['product_id' => $product->id]) }}" 
                           class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg transition-colors duration-300 text-sm font-medium {{ !$product->inStock() ? 'opacity-50 cursor-not-allowed' : '' }}"
                           {{ !$product->inStock() ? 'onclick="return false;"' : '' }}>
                            {{ $product->inStock() ? 'Buy Now' : 'Out of Stock' }}
                        </a>
                        <button class="px-3 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-300">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($products->hasPages())
        <div class="flex justify-center">
            {{ $products->appends(request()->query())->links() }}
        </div>
        @endif

        @else
        <!-- Empty State -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-12 text-center">
            <div class="w-24 h-24 mx-auto mb-4 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"/>
                </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No products found</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                We couldn't find any products in this category matching your criteria.
            </p>
            <div class="space-x-4">
                <a href="{{ route('shop.category', $category->slug) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-300">
                    Clear Filters
                </a>
                <a href="{{ route('shop.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-6 rounded-lg transition-colors duration-300">
                    Browse All Products
                </a>
            </div>
        </div>
        @endif

        <!-- Category Navigation -->
        <div class="mt-12 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Browse Other Categories</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                @foreach(\App\Models\Category::active()->where('id', '!=', $category->id)->take(4)->get() as $otherCategory)
                <a href="{{ route('shop.category', $otherCategory->slug) }}" class="group">
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-300">
                        @if($otherCategory->image)
                        <img src="{{ asset('storage/' . $otherCategory->image) }}" alt="{{ $otherCategory->name }}" class="w-12 h-12 mx-auto mb-2 rounded-full object-cover">
                        @else
                        <div class="w-12 h-12 mx-auto mb-2 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                            <span class="text-xl">📦</span>
                        </div>
                        @endif
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400">
                            {{ $otherCategory->name }}
                        </h4>
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            {{ $otherCategory->activeProducts->count() }} products
                        </p>
                    </div>
                </a>
                @endforeach
            </div>
        </div>
    </div>
</div>
@endsection
