<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\AdminSetting;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Create default admin settings
        $settings = [
            [
                'key' => 'shop_name',
                'value' => 'PolyPay Store',
                'type' => 'string',
                'description' => 'Name of the shop'
            ],
            [
                'key' => 'shop_description',
                'value' => 'A modern cryptocurrency e-commerce store',
                'type' => 'string',
                'description' => 'Shop description'
            ],
            [
                'key' => 'admin_wallet_address',
                'value' => '',
                'type' => 'string',
                'description' => 'Admin wallet address for receiving payments'
            ],
            [
                'key' => 'guest_checkout_enabled',
                'value' => 'true',
                'type' => 'boolean',
                'description' => 'Allow guest checkout without registration'
            ],
            [
                'key' => 'payment_timeout_minutes',
                'value' => '30',
                'type' => 'integer',
                'description' => 'Payment timeout in minutes'
            ],
            [
                'key' => 'supported_currencies',
                'value' => '["USDT", "POL"]',
                'type' => 'json',
                'description' => 'Supported cryptocurrency currencies'
            ]
        ];

        foreach ($settings as $setting) {
            AdminSetting::create($setting);
        }
    }
}
