<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->string('product_name'); // snapshot of product name at time of order
            $table->string('product_sku')->nullable();
            $table->decimal('price', 10, 2); // snapshot of price at time of order
            $table->integer('quantity');
            $table->decimal('total', 10, 2); // price * quantity
            $table->json('product_data')->nullable(); // snapshot of product data
            $table->json('digital_delivery_data')->nullable(); // for digital products
            $table->boolean('is_delivered')->default(false); // for digital products
            $table->timestamp('delivered_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
