@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables */
:root {
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-crypto: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    --shadow-glow: 0 0 20px rgba(103, 126, 234, 0.3);
    --shadow-card: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Base Styles */
@layer base {
    body {
        @apply font-sans antialiased;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    }
    
    .dark body {
        background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    }
}

/* Component Styles */
@layer components {
    /* Gradient Buttons */
    .btn-gradient {
        @apply px-6 py-3 rounded-lg font-semibold text-white transition-all duration-300 transform hover:scale-105;
        background: var(--gradient-primary);
        box-shadow: var(--shadow-glow);
    }
    
    .btn-gradient:hover {
        box-shadow: 0 0 30px rgba(103, 126, 234, 0.5);
    }
    
    .btn-crypto {
        @apply px-6 py-3 rounded-lg font-semibold text-white transition-all duration-300 transform hover:scale-105;
        background: var(--gradient-crypto);
        box-shadow: 0 0 20px rgba(79, 172, 254, 0.3);
    }
    
    .btn-crypto:hover {
        box-shadow: 0 0 30px rgba(79, 172, 254, 0.5);
    }
    
    /* Enhanced Cards */
    .card-modern {
        @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2;
        box-shadow: var(--shadow-card);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .card-gradient {
        @apply rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 text-white;
        background: var(--gradient-primary);
        backdrop-filter: blur(10px);
    }
    
    /* Hero Section */
    .hero-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        position: relative;
        overflow: hidden;
    }
    
    .hero-gradient::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
        opacity: 0.3;
    }
    
    /* Animated Background */
    .bg-animated {
        background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
        background-size: 400% 400%;
        animation: gradientShift 15s ease infinite;
    }
    
    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }
    
    /* Floating Animation */
    .float {
        animation: float 6s ease-in-out infinite;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }
    
    /* Pulse Animation */
    .pulse-glow {
        animation: pulseGlow 2s infinite;
    }
    
    @keyframes pulseGlow {
        0%, 100% { box-shadow: 0 0 20px rgba(103, 126, 234, 0.3); }
        50% { box-shadow: 0 0 40px rgba(103, 126, 234, 0.6); }
    }
    
    /* Glass Effect */
    .glass {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .glass-dark {
        background: rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    /* Product Cards */
    .product-card {
        @apply card-modern overflow-hidden group;
    }
    
    .product-card img {
        @apply transition-transform duration-500 group-hover:scale-110;
    }
    
    .product-card .overlay {
        @apply absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300;
    }
    
    /* Category Cards */
    .category-card {
        @apply card-modern p-6 text-center group cursor-pointer;
        background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
    }
    
    .dark .category-card {
        background: linear-gradient(135deg, rgba(45,55,72,0.9) 0%, rgba(45,55,72,0.7) 100%);
    }
    
    /* Navigation Enhancements */
    .nav-modern {
        @apply glass backdrop-blur-md;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    
    /* Form Enhancements */
    .form-modern {
        @apply w-full px-4 py-3 rounded-lg border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none transition-colors duration-200;
        background: rgba(255, 255, 255, 0.9);
    }
    
    .dark .form-modern {
        background: rgba(45, 55, 72, 0.9);
    }
    
    /* Crypto Badge */
    .crypto-badge {
        @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium transition-all duration-300 hover:scale-105;
        background: var(--gradient-crypto);
        color: white;
        box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
    }
    
    /* Loading Spinner */
    .spinner {
        @apply inline-block w-6 h-6 border-2 border-current border-r-transparent rounded-full;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    
    /* Scroll Animations */
    .fade-in {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease;
    }
    
    .fade-in.visible {
        opacity: 1;
        transform: translateY(0);
    }
}
