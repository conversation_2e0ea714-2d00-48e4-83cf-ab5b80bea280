<?php

namespace App\Helpers;

use App\Services\CurrencyService;
use Illuminate\Support\Facades\Cache;

class CurrencyHelper
{
    protected static $currencyService;

    /**
     * Get the currency service instance
     */
    protected static function getCurrencyService(): CurrencyService
    {
        if (!static::$currencyService) {
            static::$currencyService = app(CurrencyService::class);
        }
        return static::$currencyService;
    }

    /**
     * Format amount with currency symbol
     */
    public static function format(float $amount, string $currency): string
    {
        $symbols = config('currency.display.symbols', []);
        $decimals = config('currency.display.decimal_places', []);
        
        $symbol = $symbols[$currency] ?? $currency;
        $decimalPlaces = $decimals[$currency] ?? 2;
        
        return $symbol . number_format($amount, $decimalPlaces);
    }

    /**
     * Convert amount from one currency to another
     */
    public static function convert(float $amount, string $fromCurrency, string $toCurrency): float
    {
        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        $currencyService = static::getCurrencyService();
        $conversion = $currencyService->convertFiat($amount, $fromCurrency, $toCurrency);
        
        return $conversion['amount'];
    }

    /**
     * Get formatted amount in selected currency
     */
    public static function formatInSelectedCurrency(float $usdAmount, string $selectedCurrency = null): string
    {
        $selectedCurrency = $selectedCurrency ?? session('selected_currency', 'USD');
        
        if ($selectedCurrency === 'USD') {
            return static::format($usdAmount, 'USD');
        }
        
        $convertedAmount = static::convert($usdAmount, 'USD', $selectedCurrency);
        return static::format($convertedAmount, $selectedCurrency);
    }

    /**
     * Get crypto conversion preview
     */
    public static function getCryptoPreview(float $amount, string $fiatCurrency, string $cryptoCurrency): array
    {
        $currencyService = static::getCurrencyService();
        
        try {
            return $currencyService->convertFiatToCrypto($amount, $fiatCurrency, $cryptoCurrency);
        } catch (\Exception $e) {
            return [
                'crypto_amount' => 0,
                'crypto_amount_formatted' => '0',
                'fiat_amount' => $amount,
                'fiat_currency' => $fiatCurrency,
                'crypto_currency' => $cryptoCurrency,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get current crypto prices
     */
    public static function getCryptoPrices(): array
    {
        return Cache::remember('crypto_prices_display', 300, function () {
            $currencyService = static::getCurrencyService();
            return $currencyService->getCryptoPrices();
        });
    }

    /**
     * Get current fiat exchange rates
     */
    public static function getFiatRates(): array
    {
        return Cache::remember('fiat_rates_display', 3600, function () {
            $currencyService = static::getCurrencyService();
            return $currencyService->getFiatExchangeRates();
        });
    }

    /**
     * Get supported currencies for dropdown
     */
    public static function getSupportedCurrencies(): array
    {
        $currencies = config('currency.supported_fiat', []);
        $symbols = config('currency.display.symbols', []);
        
        $result = [];
        foreach ($currencies as $currency) {
            $result[$currency] = [
                'code' => $currency,
                'symbol' => $symbols[$currency] ?? $currency,
                'name' => static::getCurrencyName($currency)
            ];
        }
        
        return $result;
    }

    /**
     * Get currency name
     */
    public static function getCurrencyName(string $currency): string
    {
        $names = [
            'USD' => 'US Dollar',
            'EUR' => 'Euro',
            'GBP' => 'British Pound',
            'CAD' => 'Canadian Dollar',
            'AUD' => 'Australian Dollar',
            'JPY' => 'Japanese Yen',
        ];
        
        return $names[$currency] ?? $currency;
    }

    /**
     * Validate currency code
     */
    public static function isValidCurrency(string $currency): bool
    {
        $supportedFiat = config('currency.supported_fiat', []);
        $supportedCrypto = config('currency.supported_crypto', []);
        
        return in_array(strtoupper($currency), array_merge($supportedFiat, $supportedCrypto));
    }

    /**
     * Get currency symbol
     */
    public static function getSymbol(string $currency): string
    {
        $symbols = config('currency.display.symbols', []);
        return $symbols[$currency] ?? $currency;
    }

    /**
     * Get decimal places for currency
     */
    public static function getDecimalPlaces(string $currency): int
    {
        $decimals = config('currency.display.decimal_places', []);
        return $decimals[$currency] ?? 2;
    }
}
