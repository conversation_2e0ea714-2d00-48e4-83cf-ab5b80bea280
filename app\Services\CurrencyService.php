<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Exception;

class CurrencyService
{
    protected $supportedFiatCurrencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'];
    protected $supportedCryptoCurrencies = ['USDT', 'POL'];
    
    protected $cryptoDecimals = [
        'USDT' => 6,
        'POL' => 18,
        'MATIC' => 18,
    ];

    /**
     * Get real-time cryptocurrency prices in USD
     */
    public function getCryptoPrices(array $cryptos = null): array
    {
        $cryptos = $cryptos ?? $this->supportedCryptoCurrencies;
        $cacheKey = 'crypto_prices_' . implode('_', $cryptos);
        
        return Cache::remember($cacheKey, 300, function () use ($cryptos) { // Cache for 5 minutes
            try {
                // Map our currency codes to CoinGecko IDs
                $coinGeckoIds = [
                    'USDT' => 'tether',
                    'POL' => 'polygon-ecosystem-token', // Updated POL token
                    'MATIC' => 'matic-network' // Legacy MATIC for compatibility
                ];
                
                $ids = array_map(fn($crypto) => $coinGeckoIds[$crypto] ?? strtolower($crypto), $cryptos);
                $idsString = implode(',', $ids);
                
                $response = Http::timeout(10)->get('https://api.coingecko.com/api/v3/simple/price', [
                    'ids' => $idsString,
                    'vs_currencies' => 'usd',
                    'include_24hr_change' => 'true'
                ]);
                
                if (!$response->successful()) {
                    throw new Exception('CoinGecko API request failed: ' . $response->status());
                }
                
                $data = $response->json();
                $prices = [];
                
                foreach ($cryptos as $crypto) {
                    $geckoId = $coinGeckoIds[$crypto] ?? strtolower($crypto);
                    if (isset($data[$geckoId]['usd'])) {
                        $prices[$crypto] = [
                            'usd' => $data[$geckoId]['usd'],
                            'change_24h' => $data[$geckoId]['usd_24h_change'] ?? 0
                        ];
                    }
                }
                
                Log::info('Fetched crypto prices from CoinGecko', [
                    'currencies' => $cryptos,
                    'prices' => $prices
                ]);
                
                return $prices;
                
            } catch (Exception $e) {
                Log::error('Failed to fetch crypto prices', [
                    'error' => $e->getMessage(),
                    'currencies' => $cryptos
                ]);
                
                // Return fallback prices
                return $this->getFallbackCryptoPrices($cryptos);
            }
        });
    }

    /**
     * Get real-time fiat exchange rates (base: USD)
     */
    public function getFiatExchangeRates(array $currencies = null): array
    {
        $currencies = $currencies ?? $this->supportedFiatCurrencies;
        $cacheKey = 'fiat_rates_' . implode('_', $currencies);
        
        return Cache::remember($cacheKey, 3600, function () use ($currencies) { // Cache for 1 hour
            try {
                $currenciesString = implode(',', array_filter($currencies, fn($c) => $c !== 'USD'));
                
                $response = Http::timeout(10)->get('https://api.exchangerate-api.com/v4/latest/USD');
                
                if (!$response->successful()) {
                    throw new Exception('ExchangeRate API request failed: ' . $response->status());
                }
                
                $data = $response->json();
                $rates = ['USD' => 1.0]; // Base currency
                
                foreach ($currencies as $currency) {
                    if ($currency === 'USD') continue;
                    
                    if (isset($data['rates'][$currency])) {
                        $rates[$currency] = $data['rates'][$currency];
                    }
                }
                
                Log::info('Fetched fiat exchange rates', [
                    'base' => 'USD',
                    'currencies' => $currencies,
                    'rates' => $rates
                ]);
                
                return $rates;
                
            } catch (Exception $e) {
                Log::error('Failed to fetch fiat exchange rates', [
                    'error' => $e->getMessage(),
                    'currencies' => $currencies
                ]);
                
                // Return fallback rates
                return $this->getFallbackFiatRates($currencies);
            }
        });
    }

    /**
     * Convert amount from one fiat currency to another
     */
    public function convertFiat(float $amount, string $fromCurrency, string $toCurrency): array
    {
        if ($fromCurrency === $toCurrency) {
            return [
                'amount' => $amount,
                'rate' => 1.0,
                'from_currency' => $fromCurrency,
                'to_currency' => $toCurrency
            ];
        }
        
        $rates = $this->getFiatExchangeRates([$fromCurrency, $toCurrency]);
        
        if (!isset($rates[$fromCurrency]) || !isset($rates[$toCurrency])) {
            throw new Exception("Exchange rate not available for {$fromCurrency} to {$toCurrency}");
        }
        
        // Convert to USD first, then to target currency
        $usdAmount = $amount / $rates[$fromCurrency];
        $convertedAmount = $usdAmount * $rates[$toCurrency];
        $exchangeRate = $rates[$toCurrency] / $rates[$fromCurrency];
        
        return [
            'amount' => round($convertedAmount, 2),
            'rate' => $exchangeRate,
            'from_currency' => $fromCurrency,
            'to_currency' => $toCurrency
        ];
    }

    /**
     * Convert fiat amount to cryptocurrency
     */
    public function convertFiatToCrypto(float $fiatAmount, string $fiatCurrency, string $cryptoCurrency): array
    {
        // First convert fiat to USD if needed
        $usdConversion = $this->convertFiat($fiatAmount, $fiatCurrency, 'USD');
        $usdAmount = $usdConversion['amount'];
        
        // Get crypto price in USD
        $cryptoPrices = $this->getCryptoPrices([$cryptoCurrency]);
        
        if (!isset($cryptoPrices[$cryptoCurrency]['usd'])) {
            throw new Exception("Price not available for {$cryptoCurrency}");
        }
        
        $cryptoPriceUsd = $cryptoPrices[$cryptoCurrency]['usd'];
        $cryptoAmount = $usdAmount / $cryptoPriceUsd;
        
        return [
            'crypto_amount' => $cryptoAmount,
            'crypto_amount_formatted' => $this->formatCryptoAmount($cryptoAmount, $cryptoCurrency),
            'fiat_amount' => $fiatAmount,
            'fiat_currency' => $fiatCurrency,
            'crypto_currency' => $cryptoCurrency,
            'crypto_price_usd' => $cryptoPriceUsd,
            'fiat_to_usd_rate' => $usdConversion['rate'],
            'usd_amount' => $usdAmount
        ];
    }

    /**
     * Format crypto amount with proper decimal places
     */
    public function formatCryptoAmount(float $amount, string $currency): string
    {
        $decimals = $this->cryptoDecimals[$currency] ?? 8;
        
        // For display purposes, limit to reasonable decimal places
        $displayDecimals = min($decimals, 8);
        
        return number_format($amount, $displayDecimals, '.', '');
    }

    /**
     * Convert crypto amount to smallest unit (wei-like)
     */
    public function toSmallestUnit(string $amount, string $currency): string
    {
        $decimals = $this->cryptoDecimals[$currency] ?? 18;
        return bcmul($amount, bcpow('10', $decimals), 0);
    }

    /**
     * Convert from smallest unit to human readable amount
     */
    public function fromSmallestUnit(string $amount, string $currency): string
    {
        $decimals = $this->cryptoDecimals[$currency] ?? 18;
        return bcdiv($amount, bcpow('10', $decimals), $decimals);
    }

    /**
     * Get supported currencies
     */
    public function getSupportedFiatCurrencies(): array
    {
        return $this->supportedFiatCurrencies;
    }

    public function getSupportedCryptoCurrencies(): array
    {
        return $this->supportedCryptoCurrencies;
    }

    /**
     * Validate currency conversion rates for reasonableness
     */
    public function validateConversionRates(array $rates): bool
    {
        foreach ($rates as $currency => $rate) {
            // Check for extreme values that might indicate API errors
            if ($rate <= 0 || $rate > 1000000) {
                Log::warning('Suspicious exchange rate detected', [
                    'currency' => $currency,
                    'rate' => $rate
                ]);
                return false;
            }
        }
        return true;
    }

    /**
     * Fallback crypto prices when API is unavailable
     */
    protected function getFallbackCryptoPrices(array $cryptos): array
    {
        $fallbackPrices = [
            'USDT' => ['usd' => 1.0, 'change_24h' => 0],
            'POL' => ['usd' => 0.5, 'change_24h' => 0], // Approximate fallback
            'MATIC' => ['usd' => 0.8, 'change_24h' => 0] // Legacy fallback
        ];
        
        $prices = [];
        foreach ($cryptos as $crypto) {
            if (isset($fallbackPrices[$crypto])) {
                $prices[$crypto] = $fallbackPrices[$crypto];
            }
        }
        
        Log::warning('Using fallback crypto prices', ['prices' => $prices]);
        return $prices;
    }

    /**
     * Fallback fiat rates when API is unavailable
     */
    protected function getFallbackFiatRates(array $currencies): array
    {
        $fallbackRates = [
            'USD' => 1.0,
            'EUR' => 0.85,
            'GBP' => 0.73,
            'CAD' => 1.35,
            'AUD' => 1.50,
            'JPY' => 110.0
        ];
        
        $rates = [];
        foreach ($currencies as $currency) {
            if (isset($fallbackRates[$currency])) {
                $rates[$currency] = $fallbackRates[$currency];
            }
        }
        
        Log::warning('Using fallback fiat rates', ['rates' => $rates]);
        return $rates;
    }
}
