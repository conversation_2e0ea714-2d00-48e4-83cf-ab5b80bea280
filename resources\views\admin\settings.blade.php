@extends('layouts.admin')

@section('title', 'Settings')

@section('header')
    <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
        {{ __('Shop Settings') }}
    </h2>
@endsection

@section('content')
<div class="space-y-6">
    <!-- General Settings -->
    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-6">General Settings</h3>
            
            <form action="{{ route('admin.settings.update') }}" method="POST" class="space-y-6">
                @csrf
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Shop Name -->
                    <div>
                        <label for="shop_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Shop Name *
                        </label>
                        <input type="text" id="shop_name" name="shop_name" required
                            value="{{ old('shop_name', $settings['shop_name']->value ?? 'PolyPay Store') }}"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        @error('shop_name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Payment Timeout -->
                    <div>
                        <label for="payment_timeout_minutes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Payment Timeout (minutes) *
                        </label>
                        <input type="number" id="payment_timeout_minutes" name="payment_timeout_minutes" required min="5" max="120"
                            value="{{ old('payment_timeout_minutes', $settings['payment_timeout_minutes']->value ?? 30) }}"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        @error('payment_timeout_minutes')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">How long customers have to complete payment</p>
                    </div>
                </div>

                <!-- Shop Description -->
                <div>
                    <label for="shop_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Shop Description
                    </label>
                    <textarea id="shop_description" name="shop_description" rows="3"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">{{ old('shop_description', $settings['shop_description']->value ?? '') }}</textarea>
                    @error('shop_description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Admin Wallet Address -->
                <div>
                    <label for="admin_wallet_address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Admin Wallet Address
                    </label>
                    <input type="text" id="admin_wallet_address" name="admin_wallet_address"
                        value="{{ old('admin_wallet_address', $settings['admin_wallet_address']->value ?? '') }}"
                        placeholder="0x..."
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                    @error('admin_wallet_address')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Polygon wallet address where payments will be forwarded</p>
                </div>

                <!-- Guest Checkout -->
                <div class="flex items-center">
                    <input type="checkbox" id="guest_checkout_enabled" name="guest_checkout_enabled" value="1"
                        {{ old('guest_checkout_enabled', $settings['guest_checkout_enabled']->getTypedValue() ?? true) ? 'checked' : '' }}
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="guest_checkout_enabled" class="ml-2 block text-sm text-gray-900 dark:text-white">
                        Allow guest checkout (customers can purchase without creating an account)
                    </label>
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-300">
                        Save Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Cryptocurrency Settings -->
    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-6">Cryptocurrency Settings</h3>
            
            <div class="space-y-4">
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <h4 class="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-2">Supported Currencies</h4>
                    <div class="flex space-x-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            USDT (Tether)
                        </span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                            POL (Polygon)
                        </span>
                    </div>
                    <p class="text-sm text-blue-800 dark:text-blue-200 mt-2">All payments are processed on the Polygon network</p>
                </div>

                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                    <h4 class="text-sm font-semibold text-yellow-900 dark:text-yellow-100 mb-2">Network Configuration</h4>
                    <div class="text-sm text-yellow-800 dark:text-yellow-200 space-y-1">
                        <p><strong>Network:</strong> {{ config('web3.use_testnet') ? 'Polygon Mumbai Testnet' : 'Polygon Mainnet' }}</p>
                        <p><strong>Chain ID:</strong> {{ config('web3.use_testnet') ? config('web3.polygon_testnet_chain_id') : config('web3.polygon_chain_id') }}</p>
                        <p><strong>RPC URL:</strong> {{ config('web3.use_testnet') ? config('web3.polygon_testnet_rpc_url') : config('web3.polygon_rpc_url') }}</p>
                    </div>
                </div>

                @if(config('web3.use_testnet'))
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                    <h4 class="text-sm font-semibold text-red-900 dark:text-red-100 mb-2">⚠️ Testnet Mode</h4>
                    <p class="text-sm text-red-800 dark:text-red-200">
                        The application is currently running in testnet mode. This is for development and testing purposes only. 
                        No real cryptocurrency transactions will be processed.
                    </p>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-6">System Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">Application</h4>
                    <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                        <div class="flex justify-between">
                            <span>Laravel Version:</span>
                            <span>{{ app()->version() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>PHP Version:</span>
                            <span>{{ PHP_VERSION }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Environment:</span>
                            <span class="capitalize">{{ app()->environment() }}</span>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">Database</h4>
                    <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                        <div class="flex justify-between">
                            <span>Connection:</span>
                            <span>{{ config('database.default') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Database:</span>
                            <span>{{ config('database.connections.' . config('database.default') . '.database') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Host:</span>
                            <span>{{ config('database.connections.' . config('database.default') . '.host') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup & Maintenance -->
    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-6">Maintenance</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button type="button" class="flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
                    <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
                    </svg>
                    <span class="text-blue-600 font-medium">Export Data</span>
                </button>
                
                <button type="button" class="flex items-center justify-center p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
                    <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    <span class="text-green-600 font-medium">Clear Cache</span>
                </button>
                
                <button type="button" class="flex items-center justify-center p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors">
                    <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                    <span class="text-yellow-600 font-medium">View Logs</span>
                </button>
            </div>
        </div>
    </div>
</div>
@endsection
