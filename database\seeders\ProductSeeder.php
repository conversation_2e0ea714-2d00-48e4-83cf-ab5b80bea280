<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\Product;
use App\Models\DigitalProduct;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create categories
        $electronics = Category::firstOrCreate(
            ['slug' => 'electronics'],
            [
                'name' => 'Electronics',
                'description' => 'Electronic devices and gadgets',
                'is_active' => true,
                'sort_order' => 1,
            ]
        );

        $digital = Category::firstOrCreate(
            ['slug' => 'digital-products'],
            [
                'name' => 'Digital Products',
                'description' => 'Digital downloads and services',
                'is_active' => true,
                'sort_order' => 2,
            ]
        );

        $books = Category::firstOrCreate(
            ['slug' => 'books'],
            [
                'name' => 'Books',
                'description' => 'Physical and digital books',
                'is_active' => true,
                'sort_order' => 3,
            ]
        );

        // Create physical products
        $laptop = Product::create([
            'name' => 'Gaming Laptop Pro',
            'slug' => 'gaming-laptop-pro',
            'description' => 'High-performance gaming laptop with RTX 4080 graphics card, 32GB RAM, and 1TB SSD. Perfect for gaming, content creation, and professional work.',
            'short_description' => 'High-performance gaming laptop with RTX 4080',
            'type' => 'physical',
            'price' => 1299.99,
            'compare_price' => 1499.99,
            'stock_quantity' => 10,
            'track_stock' => true,
            'is_active' => true,
            'is_featured' => true,
            'weight' => 2.5,
            'dimensions' => ['length' => 35, 'width' => 25, 'height' => 2],
            'category_id' => $electronics->id,
        ]);

        $smartphone = Product::create([
            'name' => 'Smartphone X1',
            'slug' => 'smartphone-x1',
            'description' => 'Latest flagship smartphone with 5G connectivity, triple camera system, and all-day battery life.',
            'short_description' => 'Latest flagship smartphone with 5G',
            'type' => 'physical',
            'price' => 799.99,
            'stock_quantity' => 25,
            'track_stock' => true,
            'is_active' => true,
            'is_featured' => true,
            'weight' => 0.2,
            'dimensions' => ['length' => 15, 'width' => 7, 'height' => 0.8],
            'category_id' => $electronics->id,
        ]);

        // Create digital products
        $ebook = Product::create([
            'name' => 'Cryptocurrency Trading Guide',
            'slug' => 'crypto-trading-guide',
            'description' => 'Complete guide to cryptocurrency trading with strategies, risk management, and market analysis techniques.',
            'short_description' => 'Complete guide to cryptocurrency trading',
            'type' => 'digital',
            'price' => 29.99,
            'compare_price' => 49.99,
            'stock_quantity' => 0,
            'track_stock' => false,
            'is_active' => true,
            'is_featured' => true,
            'category_id' => $digital->id,
        ]);

        // Create digital product data for ebook
        DigitalProduct::create([
            'product_id' => $ebook->id,
            'digital_type' => 'file',
            'file_path' => 'digital/crypto-trading-guide.pdf',
            'is_unlimited' => true,
            'download_limit' => 5,
            'download_expiry_days' => 30,
        ]);

        $software = Product::create([
            'name' => 'Premium Photo Editor',
            'slug' => 'premium-photo-editor',
            'description' => 'Professional photo editing software with advanced features, filters, and AI-powered tools.',
            'short_description' => 'Professional photo editing software',
            'type' => 'digital',
            'price' => 99.99,
            'stock_quantity' => 0,
            'track_stock' => false,
            'is_active' => true,
            'category_id' => $digital->id,
        ]);

        // Create digital product data for software
        DigitalProduct::create([
            'product_id' => $software->id,
            'digital_type' => 'text',
            'content' => 'License Key: PHOTO-EDIT-2024-PREMIUM-LICENSE',
            'is_unlimited' => false,
            'total_quantity' => 100,
            'available_quantity' => 100,
        ]);

        $accounts = Product::create([
            'name' => 'Premium Streaming Accounts',
            'slug' => 'premium-streaming-accounts',
            'description' => 'Premium streaming service accounts with full access to all content.',
            'short_description' => 'Premium streaming service accounts',
            'type' => 'digital',
            'price' => 9.99,
            'stock_quantity' => 50,
            'track_stock' => true,
            'is_active' => true,
            'category_id' => $digital->id,
        ]);

        // Create digital product data for accounts
        DigitalProduct::create([
            'product_id' => $accounts->id,
            'digital_type' => 'account_credentials',
            'account_data' => [
                'accounts' => [
                    ['username' => '<EMAIL>', 'password' => 'password123'],
                    ['username' => '<EMAIL>', 'password' => 'password456'],
                    ['username' => '<EMAIL>', 'password' => 'password789'],
                ]
            ],
            'total_quantity' => 50,
            'available_quantity' => 50,
        ]);

        $physicalBook = Product::create([
            'name' => 'Web Development Handbook',
            'slug' => 'web-development-handbook',
            'description' => 'Comprehensive handbook covering modern web development technologies, frameworks, and best practices.',
            'short_description' => 'Comprehensive web development handbook',
            'type' => 'physical',
            'price' => 39.99,
            'stock_quantity' => 15,
            'track_stock' => true,
            'is_active' => true,
            'weight' => 0.8,
            'dimensions' => ['length' => 24, 'width' => 17, 'height' => 3],
            'category_id' => $books->id,
        ]);
    }
}
