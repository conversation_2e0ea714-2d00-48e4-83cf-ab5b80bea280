<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Wallet extends Model
{
    use HasFactory;

    protected $fillable = [
        'address',
        'private_key',
        'currency',
        'status',
        'payment_id',
        'used_at',
        'expires_at',
    ];

    protected $casts = [
        'used_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the payment that owns the wallet
     */
    public function payment()
    {
        return $this->belongsTo(Payment::class);
    }

    /**
     * Check if wallet is active
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && $this->expires_at > now();
    }

    /**
     * Check if wallet is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at <= now();
    }
}
