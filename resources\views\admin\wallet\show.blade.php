@extends('layouts.admin')

@section('title', 'Wallet Details - ' . Str::limit($wallet->address, 20))

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Wallet Details</h1>
        <a href="{{ route('admin.wallet.index') }}" 
           class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
            ← Back to Wallet Management
        </a>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Wallet Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">💼 Wallet Information</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Wallet Address</label>
                        <div class="flex items-center space-x-2">
                            <code class="flex-1 px-3 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-mono">
                                {{ $wallet->address }}
                            </code>
                            <button onclick="copyToClipboard('{{ $wallet->address }}')" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm transition duration-200">
                                📋 Copy
                            </button>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Currency</label>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                {{ $wallet->currency }}
                            </span>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                @switch($wallet->status)
                                    @case('active') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 @break
                                    @case('used') bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 @break
                                    @case('expired') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @break
                                    @default bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                                @endswitch">
                                {{ ucfirst($wallet->status) }}
                            </span>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Current Balance</label>
                        <div class="flex items-center space-x-2">
                            <span class="text-2xl font-bold text-gray-900 dark:text-white">
                                {{ $balance }} {{ $wallet->currency }}
                            </span>
                            <button onclick="refreshBalance()" 
                                    class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition duration-200">
                                🔄 Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            @if($wallet->payment)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">💳 Associated Payment</h2>
                
                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Payment ID</label>
                            <p class="text-gray-900 dark:text-white font-mono">{{ $wallet->payment->payment_id }}</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Payment Status</label>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                @switch($wallet->payment->status)
                                    @case('confirmed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 @break
                                    @case('pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 @break
                                    @case('expired') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 @break
                                    @default bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                                @endswitch">
                                {{ ucfirst($wallet->payment->status) }}
                            </span>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Amount</label>
                            <p class="text-lg font-semibold text-gray-900 dark:text-white">
                                {{ $wallet->payment->amount }} {{ $wallet->payment->currency }}
                            </p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Created</label>
                            <p class="text-gray-900 dark:text-white">{{ $wallet->payment->created_at->format('M j, Y g:i A') }}</p>
                        </div>
                    </div>

                    @if($wallet->payment->order)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Order Information</label>
                        <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                            <p class="text-sm">
                                <strong>Order #:</strong> {{ $wallet->payment->order->order_number }}<br>
                                <strong>Customer:</strong> {{ $wallet->payment->order->customer_name }}<br>
                                <strong>Total:</strong> {{ $wallet->payment->order->total_amount }} {{ $wallet->payment->order->currency }}
                            </p>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar Information -->
        <div class="space-y-6">
            <!-- Wallet Status -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">📊 Wallet Status</h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Active Status</label>
                        <div class="flex items-center space-x-2">
                            @if($wallet->isActive())
                                <span class="text-green-600 dark:text-green-400">✅</span>
                                <span class="text-green-600 dark:text-green-400 font-medium">Active</span>
                            @else
                                <span class="text-red-600 dark:text-red-400">❌</span>
                                <span class="text-red-600 dark:text-red-400 font-medium">Inactive</span>
                            @endif
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Expiry Status</label>
                        <div class="flex items-center space-x-2">
                            @if($wallet->isExpired())
                                <span class="text-red-600 dark:text-red-400">⏰</span>
                                <span class="text-red-600 dark:text-red-400 font-medium">Expired</span>
                            @else
                                <span class="text-green-600 dark:text-green-400">⏳</span>
                                <span class="text-green-600 dark:text-green-400 font-medium">Valid</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timestamps -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">📅 Timestamps</h2>
                
                <div class="space-y-3 text-sm">
                    <div>
                        <label class="block text-gray-700 dark:text-gray-300 font-medium">Created</label>
                        <p class="text-gray-900 dark:text-white">{{ $wallet->created_at->format('M j, Y \a\t g:i A') }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 dark:text-gray-300 font-medium">Last Updated</label>
                        <p class="text-gray-900 dark:text-white">{{ $wallet->updated_at->format('M j, Y \a\t g:i A') }}</p>
                    </div>
                    
                    @if($wallet->expires_at)
                    <div>
                        <label class="block text-gray-700 dark:text-gray-300 font-medium">Expires At</label>
                        <p class="text-gray-900 dark:text-white">{{ $wallet->expires_at->format('M j, Y \a\t g:i A') }}</p>
                        <p class="text-xs {{ $wallet->isExpired() ? 'text-red-600' : 'text-green-600' }}">
                            {{ $wallet->isExpired() ? 'Expired' : 'Expires in ' . $wallet->expires_at->diffForHumans() }}
                        </p>
                    </div>
                    @endif
                    
                    @if($wallet->used_at)
                    <div>
                        <label class="block text-gray-700 dark:text-gray-300 font-medium">Used At</label>
                        <p class="text-gray-900 dark:text-white">{{ $wallet->used_at->format('M j, Y \a\t g:i A') }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">⚡ Quick Actions</h2>
                
                <div class="space-y-3">
                    <button onclick="refreshBalance()" 
                            class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                        🔄 Refresh Balance
                    </button>
                    
                    <button onclick="copyToClipboard('{{ $wallet->address }}')" 
                            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200">
                        📋 Copy Address
                    </button>
                    
                    @if($wallet->payment)
                        <a href="{{ route('admin.payments.show', $wallet->payment) }}" 
                           class="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                            💳 View Payment
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const originalText = event.target.textContent;
        event.target.textContent = '✅ Copied!';
        event.target.classList.add('bg-green-600');
        event.target.classList.remove('bg-blue-600');
        
        setTimeout(() => {
            event.target.textContent = originalText;
            event.target.classList.remove('bg-green-600');
            event.target.classList.add('bg-blue-600');
        }, 2000);
    }).catch(function(err) {
        alert('Failed to copy to clipboard');
    });
}

function refreshBalance() {
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = '🔄 Checking...';
    button.disabled = true;
    
    fetch('{{ route("admin.wallet.check-balance") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            address: '{{ $wallet->address }}',
            currency: '{{ $wallet->currency }}'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update balance display
            const balanceElement = document.querySelector('.text-2xl.font-bold');
            balanceElement.textContent = data.formatted_balance;
            
            button.textContent = '✅ Updated!';
            button.classList.add('bg-green-600');
            button.classList.remove('bg-green-600');
        } else {
            button.textContent = '❌ Failed';
            button.classList.add('bg-red-600');
            button.classList.remove('bg-green-600');
        }
    })
    .catch(error => {
        button.textContent = '❌ Error';
        button.classList.add('bg-red-600');
        button.classList.remove('bg-green-600');
    })
    .finally(() => {
        setTimeout(() => {
            button.textContent = originalText;
            button.disabled = false;
            button.classList.remove('bg-green-600', 'bg-red-600');
            button.classList.add('bg-green-600');
        }, 2000);
    });
}
</script>
@endsection
