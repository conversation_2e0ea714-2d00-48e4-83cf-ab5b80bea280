<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'payment_id',
        'payment_method',
        'crypto_currency',
        'wallet_address',
        'amount',
        'amount_usd',
        'original_currency',
        'original_amount',
        'crypto_price_usd',
        'fiat_to_usd_rate',
        'conversion_timestamp',
        'conversion_data',
        'transaction_hash',
        'status',
        'confirmed_at',
        'expires_at',
        'blockchain_data',
        'failure_reason',
    ];

    protected $casts = [
        'amount' => 'decimal:8',
        'amount_usd' => 'decimal:2',
        'original_amount' => 'decimal:2',
        'crypto_price_usd' => 'decimal:8',
        'fiat_to_usd_rate' => 'decimal:6',
        'conversion_data' => 'array',
        'blockchain_data' => 'array',
        'confirmed_at' => 'datetime',
        'expires_at' => 'datetime',
        'conversion_timestamp' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($payment) {
            if (empty($payment->payment_id)) {
                $payment->payment_id = 'PAY-' . strtoupper(Str::random(12));
            }
        });
    }

    /**
     * Get the order that owns the payment
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the wallet for this payment
     */
    public function wallet()
    {
        return $this->hasOne(Wallet::class);
    }

    /**
     * Check if payment is confirmed
     */
    public function isConfirmed(): bool
    {
        return $this->status === 'confirmed';
    }

    /**
     * Check if payment is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if payment is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at <= now();
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 8) . ' ' . $this->crypto_currency;
    }

    /**
     * Get formatted USD amount
     */
    public function getFormattedAmountUsdAttribute(): string
    {
        return '$' . number_format($this->amount_usd, 2);
    }
}
