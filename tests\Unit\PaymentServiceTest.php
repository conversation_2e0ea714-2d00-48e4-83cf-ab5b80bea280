<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Services\PaymentService;
use App\Services\WalletService;
use App\Services\DigitalDeliveryService;
use App\Services\CurrencyService;
use App\Models\Order;
use App\Models\Payment;
use App\Models\Product;
use App\Models\Category;
use App\Models\Wallet;
use Mockery;

class PaymentServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $paymentService;
    protected $walletService;
    protected $digitalDeliveryService;
    protected $currencyService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->walletService = Mockery::mock(WalletService::class);
        $this->digitalDeliveryService = Mockery::mock(DigitalDeliveryService::class);
        $this->currencyService = Mockery::mock(CurrencyService::class);

        $this->paymentService = new PaymentService(
            $this->walletService,
            $this->digitalDeliveryService,
            $this->currencyService
        );
    }

    public function test_create_payment_generates_unique_payment_id()
    {
        $category = Category::factory()->create();
        $product = Product::factory()->create(['category_id' => $category->id]);
        $order = Order::factory()->create();

        $this->walletService
            ->shouldReceive('generateWallet')
            ->once()
            ->andReturn([
                'address' => '0x1234567890abcdef',
                'private_key' => 'mock_private_key'
            ]);

        $this->walletService
            ->shouldReceive('createWalletRecord')
            ->once()
            ->andReturnUsing(function($payment, $walletData, $currency) {
                return Wallet::factory()->create([
                    'address' => $walletData['address'],
                    'currency' => $currency,
                    'payment_id' => $payment->id
                ]);
            });

        $this->currencyService
            ->shouldReceive('convertFiatToCrypto')
            ->with(100.0, 'USD', 'USDT')
            ->once()
            ->andReturn([
                'crypto_amount' => 100.0,
                'crypto_amount_formatted' => '100.00000000',
                'fiat_amount' => 100.0,
                'fiat_currency' => 'USD',
                'crypto_currency' => 'USDT',
                'crypto_price_usd' => 1.0,
                'fiat_to_usd_rate' => 1.0,
                'usd_amount' => 100.0
            ]);

        $payment = $this->paymentService->createPayment($order, 'USDT', 100.0, 'USD');

        $this->assertInstanceOf(Payment::class, $payment);
        $this->assertEquals('USDT', $payment->crypto_currency);
        $this->assertEquals('pending', $payment->status);
        $this->assertNotNull($payment->payment_id);
        $this->assertTrue(strlen($payment->payment_id) > 10); // Should have reasonable length
    }

    public function test_create_payment_calculates_crypto_amount_for_usdt()
    {
        $category = Category::factory()->create();
        $product = Product::factory()->create(['category_id' => $category->id]);
        $order = Order::factory()->create();

        $this->walletService
            ->shouldReceive('generateWallet')
            ->once()
            ->andReturn([
                'address' => '0x1234567890abcdef',
                'private_key' => 'mock_private_key'
            ]);

        $this->walletService
            ->shouldReceive('createWalletRecord')
            ->once()
            ->andReturnUsing(function($payment, $walletData, $currency) {
                return Wallet::factory()->create([
                    'address' => $walletData['address'],
                    'currency' => $currency,
                    'payment_id' => $payment->id
                ]);
            });

        $usdAmount = 100.00;
        $payment = $this->paymentService->createPayment($order, 'USDT', $usdAmount);

        // USDT should be approximately 1:1 with USD
        $this->assertEqualsWithDelta(100.0, $payment->amount, 5.0); // Allow 5% variance
        $this->assertEquals($usdAmount, $payment->amount_usd);
    }

    public function test_create_payment_calculates_crypto_amount_for_pol()
    {
        $category = Category::factory()->create();
        $product = Product::factory()->create(['category_id' => $category->id]);
        $order = Order::factory()->create();

        $this->walletService
            ->shouldReceive('generateWallet')
            ->once()
            ->andReturn([
                'address' => '0x1234567890abcdef',
                'private_key' => 'mock_private_key'
            ]);

        $this->walletService
            ->shouldReceive('createWalletRecord')
            ->once()
            ->andReturnUsing(function($payment, $walletData, $currency) {
                return Wallet::factory()->create([
                    'address' => $walletData['address'],
                    'currency' => $currency,
                    'payment_id' => $payment->id
                ]);
            });

        $usdAmount = 100.00;
        $payment = $this->paymentService->createPayment($order, 'POL', $usdAmount);

        // POL amount should be calculated based on conversion rate
        $this->assertGreaterThan(0, $payment->amount);
        $this->assertEquals($usdAmount, $payment->amount_usd);
    }

    public function test_confirm_payment_updates_status()
    {
        $category = Category::factory()->create();
        $product = Product::factory()->create(['category_id' => $category->id]);
        $order = Order::factory()->create();

        $payment = Payment::factory()->create([
            'order_id' => $order->id,
            'status' => 'pending'
        ]);

        // For this test, we'll use a balance-based transaction hash
        // which doesn't require Web3Service mocking

        $this->digitalDeliveryService
            ->shouldReceive('processOrderDelivery')
            ->once()
            ->with($order)
            ->andReturn([
                'delivered_items' => [],
                'errors' => []
            ]);

        $this->paymentService->confirmPayment($payment, 'balance_test_payment_123');

        $payment->refresh();
        $this->assertEquals('confirmed', $payment->status);
        $this->assertEquals('balance_test_payment_123', $payment->transaction_hash);
        $this->assertNotNull($payment->confirmed_at);
        $this->assertNotNull($payment->blockchain_data);
        $this->assertEquals('balance_based', $payment->blockchain_data['confirmation_type']);
    }

    public function test_get_payment_stats_returns_correct_data()
    {
        // Create test orders first
        $order1 = Order::factory()->create();
        $order2 = Order::factory()->create();
        $order3 = Order::factory()->create();
        $order4 = Order::factory()->create();

        // Create test payments with different statuses
        Payment::factory()->create(['status' => 'pending', 'order_id' => $order1->id]);
        Payment::factory()->create(['status' => 'confirmed', 'order_id' => $order2->id]);
        Payment::factory()->create(['status' => 'confirmed', 'order_id' => $order3->id]);
        Payment::factory()->create(['status' => 'expired', 'order_id' => $order4->id]);

        $stats = $this->paymentService->getPaymentStats();

        $this->assertEquals(4, $stats['total_payments']);
        $this->assertEquals(2, $stats['confirmed_payments']);
        $this->assertEquals(1, $stats['pending_payments']);
        $this->assertEquals(1, $stats['expired_payments']);
    }

    public function test_monitor_payment_detects_expired_payments()
    {
        $order1 = Order::factory()->create();
        $order2 = Order::factory()->create();

        $expiredPayment = Payment::factory()->create([
            'status' => 'pending',
            'order_id' => $order1->id,
            'expires_at' => now()->subMinutes(10) // Expired 10 minutes ago
        ]);

        $activePayment = Payment::factory()->create([
            'status' => 'pending',
            'order_id' => $order2->id,
            'expires_at' => now()->addMinutes(10) // Expires in 10 minutes
        ]);

        $result = $this->paymentService->monitorPayments();

        $expiredPayment->refresh();
        $activePayment->refresh();

        $this->assertEquals('expired', $expiredPayment->status);
        $this->assertEquals('pending', $activePayment->status);
        $this->assertArrayHasKey('expired_payments', $result);
        $this->assertEquals(1, $result['expired_payments']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
