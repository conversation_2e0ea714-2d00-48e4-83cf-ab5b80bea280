<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\CurrencyService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CurrencyServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $currencyService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->currencyService = new CurrencyService();
        Cache::flush(); // Clear cache for each test
    }

    public function test_get_crypto_prices_returns_valid_data()
    {
        // Mock successful API response
        Http::fake([
            'api.coingecko.com/*' => Http::response([
                'tether' => [
                    'usd' => 1.0,
                    'usd_24h_change' => 0.1
                ],
                'polygon-ecosystem-token' => [
                    'usd' => 0.5,
                    'usd_24h_change' => -2.5
                ]
            ], 200)
        ]);

        $prices = $this->currencyService->getCryptoPrices(['USDT', 'POL']);

        $this->assertArrayHasKey('USDT', $prices);
        $this->assertArrayHasKey('POL', $prices);
        $this->assertEquals(1.0, $prices['USDT']['usd']);
        $this->assertEquals(0.5, $prices['POL']['usd']);
    }

    public function test_get_crypto_prices_handles_api_failure()
    {
        // Mock API failure
        Http::fake([
            'api.coingecko.com/*' => Http::response([], 500)
        ]);

        $prices = $this->currencyService->getCryptoPrices(['USDT', 'POL']);

        // Should return fallback prices
        $this->assertArrayHasKey('USDT', $prices);
        $this->assertArrayHasKey('POL', $prices);
        $this->assertEquals(1.0, $prices['USDT']['usd']); // Fallback USDT price
    }

    public function test_get_fiat_exchange_rates_returns_valid_data()
    {
        // Mock successful API response
        Http::fake([
            'api.exchangerate-api.com/*' => Http::response([
                'rates' => [
                    'EUR' => 0.85,
                    'GBP' => 0.73,
                    'CAD' => 1.35,
                    'AUD' => 1.50,
                    'JPY' => 110.0
                ]
            ], 200)
        ]);

        $rates = $this->currencyService->getFiatExchangeRates(['USD', 'EUR', 'GBP']);

        $this->assertArrayHasKey('USD', $rates);
        $this->assertArrayHasKey('EUR', $rates);
        $this->assertArrayHasKey('GBP', $rates);
        $this->assertEquals(1.0, $rates['USD']); // Base currency
        $this->assertEquals(0.85, $rates['EUR']);
        $this->assertEquals(0.73, $rates['GBP']);
    }

    public function test_convert_fiat_same_currency()
    {
        $result = $this->currencyService->convertFiat(100.0, 'USD', 'USD');

        $this->assertEquals(100.0, $result['amount']);
        $this->assertEquals(1.0, $result['rate']);
        $this->assertEquals('USD', $result['from_currency']);
        $this->assertEquals('USD', $result['to_currency']);
    }

    public function test_convert_fiat_different_currencies()
    {
        // Mock exchange rates
        Http::fake([
            'api.exchangerate-api.com/*' => Http::response([
                'rates' => [
                    'EUR' => 0.85,
                    'GBP' => 0.73
                ]
            ], 200)
        ]);

        $result = $this->currencyService->convertFiat(100.0, 'USD', 'EUR');

        $this->assertEquals(85.0, $result['amount']);
        $this->assertEquals(0.85, $result['rate']);
        $this->assertEquals('USD', $result['from_currency']);
        $this->assertEquals('EUR', $result['to_currency']);
    }

    public function test_convert_fiat_to_crypto()
    {
        // Mock both APIs
        Http::fake([
            'api.exchangerate-api.com/*' => Http::response([
                'rates' => [
                    'EUR' => 0.85
                ]
            ], 200),
            'api.coingecko.com/*' => Http::response([
                'tether' => [
                    'usd' => 1.0,
                    'usd_24h_change' => 0.1
                ]
            ], 200)
        ]);

        $result = $this->currencyService->convertFiatToCrypto(85.0, 'EUR', 'USDT');

        $this->assertEquals(100.0, $result['crypto_amount']); // 85 EUR = 100 USD = 100 USDT
        $this->assertEquals(85.0, $result['fiat_amount']);
        $this->assertEquals('EUR', $result['fiat_currency']);
        $this->assertEquals('USDT', $result['crypto_currency']);
        $this->assertEquals(1.0, $result['crypto_price_usd']);
    }

    public function test_format_crypto_amount()
    {
        $formatted = $this->currencyService->formatCryptoAmount(1.23456789, 'USDT');
        $this->assertEquals('1.234568', $formatted); // USDT shows 6 decimal places for display (rounded)

        $formatted = $this->currencyService->formatCryptoAmount(1.23456789, 'POL');
        $this->assertEquals('1.23456789', $formatted); // POL shows 8 decimal places for display
    }

    public function test_to_smallest_unit()
    {
        $result = $this->currencyService->toSmallestUnit('1.0', 'USDT');
        $this->assertEquals('1000000', $result); // 1 USDT = 1,000,000 smallest units (6 decimals)

        $result = $this->currencyService->toSmallestUnit('1.0', 'POL');
        $this->assertEquals('1000000000000000000', $result); // 1 POL = 10^18 smallest units
    }

    public function test_from_smallest_unit()
    {
        $result = $this->currencyService->fromSmallestUnit('1000000', 'USDT');
        $this->assertEquals('1.000000', $result); // Back to 1 USDT

        $result = $this->currencyService->fromSmallestUnit('1000000000000000000', 'POL');
        $this->assertEquals('1.000000000000000000', $result); // Back to 1 POL
    }

    public function test_validate_conversion_rates()
    {
        $validRates = [
            'EUR' => 0.85,
            'GBP' => 0.73,
            'JPY' => 110.0
        ];

        $this->assertTrue($this->currencyService->validateConversionRates($validRates));

        $invalidRates = [
            'EUR' => 0.85,
            'INVALID' => -1.0, // Negative rate
            'EXTREME' => 2000000.0 // Too high
        ];

        $this->assertFalse($this->currencyService->validateConversionRates($invalidRates));
    }

    public function test_get_supported_currencies()
    {
        $fiatCurrencies = $this->currencyService->getSupportedFiatCurrencies();
        $this->assertContains('USD', $fiatCurrencies);
        $this->assertContains('EUR', $fiatCurrencies);
        $this->assertContains('GBP', $fiatCurrencies);

        $cryptoCurrencies = $this->currencyService->getSupportedCryptoCurrencies();
        $this->assertContains('USDT', $cryptoCurrencies);
        $this->assertContains('POL', $cryptoCurrencies);
    }

    public function test_caching_works()
    {
        // Mock API response
        Http::fake([
            'api.coingecko.com/*' => Http::response([
                'tether' => [
                    'usd' => 1.0,
                    'usd_24h_change' => 0.1
                ]
            ], 200)
        ]);

        // First call should hit the API
        $prices1 = $this->currencyService->getCryptoPrices(['USDT']);

        // Second call should use cache
        $prices2 = $this->currencyService->getCryptoPrices(['USDT']);

        $this->assertEquals($prices1, $prices2);

        // Verify only one API call was made
        Http::assertSentCount(1);
    }
}
