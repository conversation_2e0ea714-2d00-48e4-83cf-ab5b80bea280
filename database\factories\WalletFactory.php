<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Wallet>
 */
class WalletFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'address' => '0x' . $this->faker->bothify('????????????????????????????????????????'),
            'private_key' => $this->faker->sha256(),
            'currency' => $this->faker->randomElement(['USDT', 'POL']),
            'status' => $this->faker->randomElement(['active', 'used', 'expired']),
            'payment_id' => null, // Should be set when creating
            'used_at' => null,
            'expires_at' => $this->faker->dateTimeBetween('now', '+2 hours'),
        ];
    }
}
