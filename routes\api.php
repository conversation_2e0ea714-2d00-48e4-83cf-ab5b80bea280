<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Services\CurrencyService;

Route::middleware(['auth:sanctum'])->get('/user', function (Request $request) {
    return $request->user();
});

// Currency and crypto price endpoints
Route::post('/crypto-prices', function (Request $request, CurrencyService $currencyService) {
    $request->validate([
        'amount' => 'required|numeric|min:0',
        'currency' => 'required|string|size:3',
        'crypto_currency' => 'required|string|in:USDT,POL'
    ]);
    
    try {
        $conversionData = $currencyService->convertFiatToCrypto(
            $request->amount,
            $request->currency,
            $request->crypto_currency
        );
        
        return response()->json($conversionData);
    } catch (Exception $e) {
        return response()->json([
            'error' => $e->getMessage()
        ], 400);
    }
});

Route::get('/exchange-rates', function (CurrencyService $currencyService) {
    try {
        return response()->json([
            'fiat_rates' => $currencyService->getFiatExchangeRates(),
            'crypto_prices' => $currencyService->getCryptoPrices(),
            'timestamp' => now()->toISOString()
        ]);
    } catch (Exception $e) {
        return response()->json([
            'error' => $e->getMessage()
        ], 500);
    }
});
